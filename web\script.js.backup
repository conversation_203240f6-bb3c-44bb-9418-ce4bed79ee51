// Agent聊天界面的JavaScript逻辑
class AgentChat {
    constructor() {
        this.isProcessing = false;
        this.currentSessionId = null;
        this.sessions = new Map();
        this.websocket = null;
        this.messageId = 0;
        this.currentModel = 'qwen3:32b'; // 默认模型
        this.typewriterIntervals = new Map(); // 存储打字机效果的定时器
        this.currentAssistantMessageId = null; // 新增：跟踪当前活跃的助手消息ID
        this.reconnectAttempts = 0; // WebSocket重连计数器

        // COT模型列表和调试模式 - 从后端配置获取
        this.cotModels = [];
        this.debugMode = false;

        this.initializeEventListeners();
        this.initializeWebSocket();
        this.loadConfig(); // 加载配置
        this.loadSystemStatus();
        this.loadAvailableModels(); // 加载可用模型
        this.createNewSession();
    }

    // 检查是否为COT模型
    isCOTModel(modelName = null) {
        const model = modelName || this.currentModel;
        return this.cotModels.some(cotModel =>
            model.toLowerCase().includes(cotModel.toLowerCase())
        );
    }

    // 处理think标签内容
    processThinkContent(content) {
        const isCOT = this.isCOTModel();
        if (this.debugMode) {
            console.log('[COT] 处理think标签 - 模型:', this.currentModel, '是否COT:', isCOT);
        }

        if (!content) {
            if (this.debugMode) {
                console.log('[COT] 内容为空，返回原内容');
            }
            return content;
        }

        if (!isCOT) {
            if (this.debugMode) {
                console.log('[COT] 非COT模型，返回原内容');
            }
            return content; // 非COT模型直接返回原内容
        }

        // 检查是否包含think标签
        const hasThinkTags = content.includes('<think>') && content.includes('</think>');
        if (this.debugMode) {
            console.log('[COT] 内容包含think标签:', hasThinkTags);
        }

        // 提取think标签内容
        const thinkRegex = /<think>([\s\S]*?)<\/think>/gi;
        const thinkMatches = [];
        let match;

        while ((match = thinkRegex.exec(content)) !== null) {
            thinkMatches.push({
                fullMatch: match[0],
                content: match[1].trim()
            });
        }

        if (this.debugMode) {
            console.log('[COT] 找到think标签数量:', thinkMatches.length);
        }

        if (thinkMatches.length === 0) {
            if (this.debugMode) {
                console.log('[COT] 没有找到think标签，返回原内容');
            }
            return content; // 没有think标签，直接返回
        }

        // 移除think标签，保留其他内容
        let processedContent = content;
        thinkMatches.forEach(thinkMatch => {
            processedContent = processedContent.replace(thinkMatch.fullMatch, '');
        });

        // 清理多余的空行
        processedContent = processedContent.replace(/\n\s*\n\s*\n/g, '\n\n').trim();

        const result = {
            content: processedContent,
            thinkingSteps: thinkMatches.map(match => ({
                content: match.content
            }))
        };

        if (this.debugMode) {
            console.log('[COT] 处理结果 - 思考步骤数:', result.thinkingSteps.length, '处理后内容长度:', processedContent.length);
        }

        return result;
    }

    // 初始化事件监听器
    initializeEventListeners() {
        const sendBtn = document.getElementById('send-btn');
        const chatInput = document.getElementById('chat-input');
        const newChatBtn = document.getElementById('new-chat-btn');
        const clearChatBtn = document.getElementById('clear-chat-btn');
        const modelSelect = document.getElementById('model-select');

        // 发送消息
        sendBtn.addEventListener('click', () => this.sendMessage());

        // 输入框事件
        chatInput.addEventListener('input', () => this.handleInputChange());
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 新对话按钮
        newChatBtn.addEventListener('click', () => this.createNewSession());

        // 清空对话按钮
        clearChatBtn.addEventListener('click', () => this.clearCurrentSession());

        // 模型切换
        modelSelect.addEventListener('change', (e) => this.handleModelChange(e.target.value));

        // 示例任务按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('example-task')) {
                const task = e.target.getAttribute('data-task');
                chatInput.value = task;
                chatInput.focus();
                this.handleInputChange();

                // 添加一个小动画效果
                e.target.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    e.target.style.transform = '';
                }, 150);
            }
        });

        // 会话切换事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.session-item')) {
                const sessionItem = e.target.closest('.session-item');
                const sessionId = sessionItem.getAttribute('data-session-id');
                this.switchToSession(sessionId);
            }

            // 新增：处理可折叠区域的点击事件
            const collapsibleHeader = e.target.closest('.tool-collapsible-header');
            if (collapsibleHeader) {
                const collapsibleSection = collapsibleHeader.parentElement;
                if (collapsibleSection && collapsibleSection.classList.contains('tool-collapsible-section')) {
                    collapsibleSection.classList.toggle('collapsed');
                    // 图标的旋转通常由CSS中的 .collapsed .toggle-icon 控制
                }
            }
        });
    }

    // 处理输入变化 - 优化滚动条逻辑
    handleInputChange() {
        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('send-btn');

        // 重置高度以获取真实的scrollHeight
        chatInput.style.height = 'auto';

        const scrollHeight = chatInput.scrollHeight;
        const maxHeight = 120; // 减少最大高度，约3-4行
        const minHeight = 44;   // 单行高度
        const lineHeight = 20;  // 估算行高

        // 计算行数
        const lines = Math.floor(scrollHeight / lineHeight);

        // 只有当内容真正超过3行时才显示滚动条
        if (lines <= 3 || scrollHeight <= maxHeight) {
            // 内容较少，不需要滚动条
            chatInput.style.height = Math.max(scrollHeight, minHeight) + 'px';
            chatInput.style.overflowY = 'hidden';
        } else {
            // 内容超过3行，显示滚动条
            chatInput.style.height = maxHeight + 'px';
            chatInput.style.overflowY = 'auto';
        }

        // 控制发送按钮状态
        const hasContent = chatInput.value.trim().length > 0;
        sendBtn.disabled = !hasContent || this.isProcessing;
    }

    // 处理模型切换
    handleModelChange(newModel) {
        this.currentModel = newModel;

        // 显示模型切换通知
        this.showNotification(`已切换到模型: ${this.getModelDisplayName(newModel)}`, 'info');

        // 更新状态显示
        this.updateModelStatus('connecting');

        // 模拟模型加载时间
        setTimeout(() => {
            this.updateModelStatus('active');
            this.showNotification(`模型 ${this.getModelDisplayName(newModel)} 已就绪`, 'success');
        }, 2000);

        // 在当前会话中添加系统消息
        if (this.currentSessionId) {
            this.addMessage('system', `已切换到模型: ${this.getModelDisplayName(newModel)}`);
        }
    }

    // 加载配置信息
    async loadConfig() {
        try {
            const response = await fetch('/api/config');
            const result = await response.json();

            if (result.success && result.config) {
                this.cotModels = result.config.cot_models || [];
                this.debugMode = result.config.debug_mode || false;
                this.currentModel = result.config.default_model || 'qwen3:32b';

                if (this.debugMode) {
                    console.log('🔧 调试模式已启用');
                    console.log('📋 COT模型列表:', this.cotModels);
                } else {
                    console.log('🔧 调试模式已禁用，控制台输出将被精简');
                }
            } else {
                console.warn('使用默认配置，原因:', result.error || '无法获取配置');
                // 使用默认配置
                this.cotModels = ['qwen3:32b', 'qwq:latest', 'deepseek-r1:32b'];
                this.debugMode = false;
            }
        } catch (error) {
            console.error('加载配置失败:', error);
            // 使用默认配置
            this.cotModels = ['qwen3:32b', 'qwq:latest', 'deepseek-r1:32b'];
            this.debugMode = false;
        }
    }

    // 加载可用模型列表
    async loadAvailableModels() {
        try {
            const response = await fetch('/api/models');
            const result = await response.json();

            if (result.success && result.models && result.models.length > 0) {
                this.updateModelSelectors(result.models);
                if (this.debugMode) {
                    console.log('已从ollama服务器加载模型列表:', result.models.length, '个模型');
                }
            } else {
                if (this.debugMode) {
                    console.warn('使用默认模型列表，原因:', result.error || '无法获取模型');
                }
                // 使用默认模型列表
                this.updateModelSelectors([
                    { name: 'qwen3:32b', description: 'Qwen3 (32B)' },
                    { name: 'gemma3:27b', description: 'Gemma3 (27B)' },
                    { name: 'qwq:latest', description: 'QWQ (latest)' },
                    { name: 'deepseek-r1:32b', description: 'DeepSeek-R1 (32B)' }
                ]);
            }
        } catch (error) {
            if (this.debugMode) {
                console.error('加载模型列表失败:', error);
            }
            // 保持默认的HTML选项
        }
    }

    // 更新模型选择器
    updateModelSelectors(models) {
        const selectors = [
            document.getElementById('model-select'),
            document.getElementById('model-name') // 设置页面中的选择器
        ];

        selectors.forEach(selector => {
            if (!selector) return;

            // 保存当前选中的值
            const currentValue = selector.value || this.currentModel;

            // 清空现有选项
            selector.innerHTML = '';

            // 添加新选项
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.name;
                option.textContent = model.description || model.name;
                selector.appendChild(option);
            });

            // 设置正确的选中状态
            if (models.some(m => m.name === this.currentModel)) {
                selector.value = this.currentModel;
            } else if (models.some(m => m.name === currentValue)) {
                selector.value = currentValue;
                this.currentModel = currentValue;
            } else if (models.length > 0) {
                // 如果之前的值不存在，选择第一个
                selector.value = models[0].name;
                this.currentModel = models[0].name;
            }

            // 添加change事件监听器（如果还没有）
            if (selector.id === 'model-name' && !selector.hasAttribute('data-listener-added')) {
                selector.addEventListener('change', (e) => this.handleModelChange(e.target.value));
                selector.setAttribute('data-listener-added', 'true');
            }
        });

        // 更新模型状态显示
        this.updateModelStatusDisplay();
    }

    // 更新模型状态显示
    updateModelStatusDisplay() {
        // 更新状态栏中的模型显示
        const modelStatusElement = document.querySelector('.model-status .status-text');
        if (modelStatusElement) {
            modelStatusElement.textContent = this.getModelDisplayName(this.currentModel);
        }

        // 更新悬停提示
        const modelStatusContainer = document.querySelector('.model-status');
        if (modelStatusContainer) {
            modelStatusContainer.title = `当前模型: ${this.getModelDisplayName(this.currentModel)}`;
        }
    }

    // 获取模型显示名称
    getModelDisplayName(modelValue) {
        const modelNames = {
            'qwen3:32b': 'Qwen3 (32B)',
            'gemma3:27b': 'Gemma3 (27B)',
            'qwq:latest': 'QWQ (latest)',
            'deepseek-r1:32b': 'DeepSeek-R1 (32B)'
        };
        return modelNames[modelValue] || modelValue;
    }

    // 创建新会话
    createNewSession() {
        const sessionId = `session_${Date.now()}`;
        const session = {
            id: sessionId,
            title: '新对话',
            messages: [],
            createdAt: new Date(),
            lastActivity: new Date()
        };

        this.sessions.set(sessionId, session);
        this.currentSessionId = sessionId;

        this.updateSessionsList();
        this.updateChatHeader();
        this.renderMessages();
        this.showWelcomeMessage();
    }

    // 切换会话
    switchToSession(sessionId) {
        if (this.sessions.has(sessionId)) {
            this.currentSessionId = sessionId;
            this.updateSessionsList();
            this.updateChatHeader();
            this.renderMessages();
        }
    }

    // 删除当前会话
    clearCurrentSession() {
        if (this.currentSessionId && this.sessions.has(this.currentSessionId)) {
            // 删除当前会话
            this.sessions.delete(this.currentSessionId);

            // 如果还有其他会话，切换到最新的会话
            if (this.sessions.size > 0) {
                const sessionsArray = Array.from(this.sessions.values())
                    .sort((a, b) => b.lastActivity - a.lastActivity);
                this.currentSessionId = sessionsArray[0].id;
                this.updateSessionsList();
                this.updateChatHeader();
                this.renderMessages();
            } else {
                // 如果没有其他会话，创建新会话
                this.createNewSession();
            }

            this.showNotification('会话已删除', 'info');
        }
    }

    // 发送消息
    async sendMessage() {
        const chatInput = document.getElementById('chat-input');
        const message = chatInput.value.trim();

        if (!message || this.isProcessing) return;

        // 清空输入框
        chatInput.value = '';
        this.handleInputChange();

        // 添加用户消息
        this.addMessage('user', message);

        // 隐藏欢迎消息
        this.hideWelcomeMessage();

        // 立即创建一个空的助手消息用于后续更新
        const assistantMessage = this.addMessage('assistant', '', {
            isActive: true,
            toolExecutions: [],
            thinkingProcess: []
        });
        this.currentAssistantMessageId = assistantMessage.id;

        // 显示打字指示器
        this.showTypingIndicator();

        // 设置处理状态
        this.isProcessing = true;

        try {
            // 发送到后端
            await this.sendToBackend(message);
        } catch (error) {
            // 更新当前助手消息显示错误
            if (this.currentAssistantMessageId) {
                const session = this.sessions.get(this.currentSessionId);
                const errorMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);
                if (errorMessage) {
                    errorMessage.content = `抱歉，发生了错误：${error.message}`;
                    errorMessage.metadata.isActive = false;
                    this.updateAssistantMessage(errorMessage);
                }
            }
            this.hideTypingIndicator();
            this.isProcessing = false;
            this.currentAssistantMessageId = null;
        }
    }

    // 发送消息到后端
    async sendToBackend(message) {
        const response = await fetch('/api/task', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                task: message,
                session_id: this.currentSessionId,
                model: this.currentModel
            })
        });

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message);
        }
    }

    // 添加消息
    addMessage(role, content, metadata = {}) {
        if (!this.currentSessionId) return;

        const session = this.sessions.get(this.currentSessionId);
        const message = {
            id: ++this.messageId,
            role: role,
            content: content,
            timestamp: new Date(),
            metadata: metadata
        };

        session.messages.push(message);
        session.lastActivity = new Date();

        // 更新会话标题（使用第一条用户消息）
        if (role === 'user' && session.title === '新对话') {
            session.title = this.generateSessionTitle(content);
        }

        this.updateSessionsList();
        this.updateChatHeader();

        // 使用增量添加而不是重新渲染整个列表
        this.appendMessage(message);

        return message;
    }

    // 生成会话标题
    generateSessionTitle(content) {
        // 清理消息内容，移除多余的空格和换行
        const cleanContent = content.trim().replace(/\s+/g, ' ');

        // 如果消息太短，直接返回
        if (cleanContent.length <= 12) {
            return cleanContent;
        }

        // 智能提取关键词和主题
        return this.extractSmartTitle(cleanContent);
    }

    // 智能标题提取 - 优化版本
    extractSmartTitle(content) {
        // 常见问题模式识别
        const patterns = [
            { regex: /^(计算|求|算)(.{1,15})/, template: '计算$2' },
            { regex: /^(搜索|查找|找)(.{1,15})/, template: '搜索$2' },
            { regex: /^(创建|写|生成)(.{1,15})/, template: '创建$2' },
            { regex: /^(帮我|请)(.{1,15})/, template: '$2' },
            { regex: /^(.{1,18})/, template: '$1' }
        ];

        for (const pattern of patterns) {
            const match = content.match(pattern.regex);
            if (match) {
                let title = pattern.template.replace(/\$(\d+)/g, (_, num) => {
                    const group = match[parseInt(num)];
                    return group ? group.trim() : '';
                });

                // 清理标题
                title = title.replace(/[？！。，、]/g, '').trim();

                // 限制长度
                if (title.length > 16) {
                    title = title.substring(0, 16);
                    // 在合适位置截断
                    const lastSpace = title.lastIndexOf(' ');
                    if (lastSpace > 8) {
                        title = title.substring(0, lastSpace);
                    }
                }

                return title + (content.length > title.length ? '...' : '');
            }
        }

        // 默认截断
        return content.substring(0, 16) + '...';
    }

    // 增量添加单个消息到DOM
    appendMessage(message) {
        const messagesContainer = document.getElementById('chat-messages');
        const messageHtml = this.renderMessage(message);

        // 创建临时容器
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = messageHtml;
        const messageElement = tempDiv.firstElementChild;

        // 添加到容器
        messagesContainer.appendChild(messageElement);

        // 平滑滚动到底部
        this.smoothScrollToBottom();
    }

    // 更新会话列表
    updateSessionsList() {
        const sessionsList = document.getElementById('sessions-list');
        const sessionsArray = Array.from(this.sessions.values())
            .sort((a, b) => b.lastActivity - a.lastActivity);

        sessionsList.innerHTML = sessionsArray.map(session => {
            const isActive = session.id === this.currentSessionId;
            const lastMessage = session.messages[session.messages.length - 1];
            const preview = lastMessage ?
                (lastMessage.role === 'user' ? lastMessage.content : '助手回复') :
                '开始新对话';

            return `
                <div class="session-item ${isActive ? 'active' : ''}" data-session-id="${session.id}">
                    <div class="session-header">
                        <div class="session-title">${session.title}</div>
                        <div class="session-time">${this.formatTime(session.lastActivity)}</div>
                    </div>
                    <div class="session-preview">${preview.length > 35 ? preview.substring(0, 35) + '...' : preview}</div>
                </div>
            `;
        }).join('');
    }

    // 更新聊天头部
    updateChatHeader() {
        if (!this.currentSessionId) return;

        const session = this.sessions.get(this.currentSessionId);
        const titleElement = document.getElementById('current-session-title');
        const subtitleElement = document.getElementById('current-session-subtitle');

        titleElement.textContent = session.title;
        subtitleElement.textContent = `${session.messages.length} 条消息 · ${this.formatTime(session.lastActivity)}`;
    }

    // 渲染消息（只在必要时使用，优先使用增量更新）
    renderMessages() {
        if (!this.currentSessionId) return;

        const session = this.sessions.get(this.currentSessionId);
        const messagesContainer = document.getElementById('chat-messages');

        if (session.messages.length === 0) {
            this.showWelcomeMessage();
            return;
        }

        // 清空容器并重新渲染所有消息（仅在切换会话时使用）
        messagesContainer.innerHTML = '';

        // 使用DocumentFragment批量添加，减少重排
        const fragment = document.createDocumentFragment();

        session.messages.forEach(message => {
            const messageHtml = this.renderMessage(message);
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = messageHtml;
            const messageElement = tempDiv.firstElementChild;
            fragment.appendChild(messageElement);
        });

        messagesContainer.appendChild(fragment);

        // 滚动到底部
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // 渲染单个消息
    renderMessage(message) {
        const time = this.formatTime(message.timestamp);

        // 根据消息类型选择不同的渲染方式
        switch (message.role) {
            case 'user':
                return this.renderUserMessage(message, time);
            case 'assistant':
                return this.renderAssistantMessage(message, time);
            case 'tool-execution':
                return this.renderToolExecutionMessage(message, time);
            case 'thinking':
                return this.renderThinkingMessage(message, time);
            case 'system':
                return this.renderSystemMessage(message, time);
            default:
                return this.renderAssistantMessage(message, time);
        }
    }

    // 渲染用户消息
    renderUserMessage(message, time) {
        return `
            <div class="message user">
                <div class="message-avatar">
                    <i class="fas fa-user"></i>
                    <div class="model-name">用户</div>
                </div>
                <div class="message-content">
                    <div class="message-bubble">${this.formatMessageContent(message.content)}</div>
                    <div class="message-time">${time}</div>
                </div>
            </div>
        `;
    }

    // 渲染助手消息
    renderAssistantMessage(message, time) {
        // 获取模型名称，优先从消息元数据中获取，否则使用当前模型
        const modelName = message.metadata?.model_name || this.currentModel;
        const modelDisplayName = this.getModelDisplayName(modelName);

        return `
            <div class="message assistant" data-message-id="${message.id}">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                    <div class="model-name">${modelDisplayName}</div>
                </div>
                <div class="message-content">
                    <div class="message-bubble">${this.renderAssistantMessageContent(message)}</div>
                    <div class="message-time">${time}</div>
                </div>
            </div>
        `;
    }

    // 更新助手消息（流畅更新，避免闪烁）
    updateAssistantMessage(message) {
        const messageElement = document.querySelector(`[data-message-id="${message.id}"]`);
        if (!messageElement) {
            this.renderMessages();
            return;
        }

        // 使用流畅的增量更新
        this.updateAssistantMessageSmooth(messageElement, message);

        // 平滑滚动到底部
        this.smoothScrollToBottom();
    }

    // 流畅更新助手消息内容
    updateAssistantMessageSmooth(messageElement, message) {
        // 更新规划区域
        this.updatePlanningSectionSmooth(messageElement, message);

        // 更新思维过程
        this.updateThinkingSectionSmooth(messageElement, message);

        // 更新LLM思考过程（来自think标签）
        this.updateLlmThinkingSectionSmooth(messageElement, message);

        // 更新工具执行
        this.updateToolsSectionSmooth(messageElement, message);

        // 更新主要回复
        this.updateMainResponseSmooth(messageElement, message);
    }

    // 流畅更新规划区域
    updatePlanningSectionSmooth(messageElement, message) {
        const planningSection = messageElement.querySelector('[data-role="planning-section"]');
        // Hide the planning section as per user request
        if (planningSection) {
            planningSection.classList.add('hidden');
            planningSection.innerHTML = ''; // Clear content as well
        }
        return;
    }

    // 流畅更新思维过程区域
    updateThinkingSectionSmooth(messageElement, message) {
        const thinkingProcess = message.metadata.thinkingProcess || [];
        const thinkingSection = messageElement.querySelector('[data-role="thinking-section"]');

        if (!thinkingSection) return;

        if (thinkingProcess.length === 0) {
            thinkingSection.classList.add('hidden');
            return;
        }

        thinkingSection.classList.remove('hidden');

        // 更新计数
        // const countElement = thinkingSection.querySelector('[data-role="thinking-count"]');
        // if (countElement) {
        //     countElement.textContent = `思维过程 (${thinkingProcess.length} 步)`;
        // } // Removed title/count as per user request

        // 增量添加新的思维步骤
        const stepsContainer = thinkingSection.querySelector('[data-role="thinking-steps"]');

        // Add null check for stepsContainer
        if (!stepsContainer) {
            console.error('[updateThinkingSectionSmooth] stepsContainer not found in thinkingSection:', thinkingSection);
            return;
        }

        const existingSteps = stepsContainer.children.length;

        for (let i = existingSteps; i < thinkingProcess.length; i++) {
            const step = thinkingProcess[i];
            const stepElement = document.createElement('div');
            stepElement.className = 'thinking-step';
            stepElement.innerHTML = `<div class="step-content">${this.formatMessageContent(step.content)}</div>`;

            // 添加淡入动画
            stepElement.style.opacity = '0';
            stepElement.style.transform = 'translateY(10px)';
            stepElement.style.transition = 'all 0.3s ease';

            stepsContainer.appendChild(stepElement);

            // 触发动画
            requestAnimationFrame(() => {
                stepElement.style.opacity = '1';
                stepElement.style.transform = 'translateY(0)';
            });
        }
    }

    // 流畅更新LLM思考过程区域（来自think标签）
    updateLlmThinkingSectionSmooth(messageElement, message) {
        const llmThinkingSection = messageElement.querySelector('[data-role="llm-thinking-section"]');
        // Hide the LLM thinking section as per user request
        if (llmThinkingSection) {
            llmThinkingSection.classList.add('hidden');
            llmThinkingSection.innerHTML = ''; // Clear content as well
        }
        return;
    }

    // 流畅更新工具执行区域
    updateToolsSectionSmooth(messageElement, message) {
        const toolExecutions = message.metadata.toolExecutions || [];
        const toolsSection = messageElement.querySelector('[data-role="tools-section"]');

        if (!toolsSection) {
            return;
        }

        if (toolExecutions.length === 0) {
            toolsSection.classList.add('hidden');
            return;
        }

        // 显示工具执行区域
        toolsSection.classList.remove('hidden');

        // 更新计数
        const countElement = toolsSection.querySelector('[data-role="tools-count"]');
        if (countElement) {
            countElement.textContent = `工具执行 (${toolExecutions.length} 个)`;
        }

        // 增量添加新的工具执行
        const executionsContainer = toolsSection.querySelector('[data-role="tool-executions"]');
        const existingExecutions = executionsContainer.children.length;

        console.log(`添加工具执行: 现有${existingExecutions}个，新增${toolExecutions.length - existingExecutions}个`);

        for (let i = existingExecutions; i < toolExecutions.length; i++) {
            const tool = toolExecutions[i];
            console.log('渲染工具执行:', tool.tool, tool.action, '结果长度:', tool.result?.length || 0);

            const toolElement = document.createElement('div');
            toolElement.innerHTML = this.renderToolExecution(tool);

            // 添加淡入动画
            toolElement.style.opacity = '0';
            toolElement.style.transform = 'translateY(10px)';
            toolElement.style.transition = 'all 0.3s ease';

            executionsContainer.appendChild(toolElement);

            // 触发动画
            requestAnimationFrame(() => {
                toolElement.style.opacity = '1';
                toolElement.style.transform = 'translateY(0)';
            });
        }
    }

    // 流畅更新主要回复区域
    updateMainResponseSmooth(messageElement, message) {
        const mainResponse = messageElement.querySelector('[data-role="main-response"]');
        if (!mainResponse) return;

        const loadingDots = mainResponse.querySelector('.loading-dots');

        console.log('更新主要回复区域:', {
            hasContent: !!message.content,
            contentLength: message.content?.length || 0,
            isActive: message.metadata.isActive,
            hasLoadingDots: !!loadingDots
        });

        if (message.content && message.content.trim()) {
            // 有内容时，移除加载指示器并显示内容
            if (loadingDots) {
                console.log('移除加载指示器');
                loadingDots.remove();
            }

            let responseContent = mainResponse.querySelector('.response-content');
            if (!responseContent) {
                responseContent = document.createElement('div');
                responseContent.className = 'response-content';
                mainResponse.appendChild(responseContent);
            }

            // 使用打字机效果更新内容
            console.log('开始打字机效果，内容长度:', message.content.length);
            this.typewriterEffect(responseContent, message.content, 20);
        } else if (message.metadata.isActive && !loadingDots) {
            // 如果还在处理中且没有加载指示器，添加一个
            console.log('添加加载指示器');
            const newLoadingDots = document.createElement('div');
            newLoadingDots.className = 'loading-dots';
            newLoadingDots.textContent = '正在思考中...';
            mainResponse.appendChild(newLoadingDots);
        } else if (!message.metadata.isActive && loadingDots) {
            // 如果不再活跃但还有加载指示器，移除它
            console.log('任务完成，移除加载指示器');
            loadingDots.remove();
        }
    }

    // 增量更新助手消息（避免闪烁的优化版本）
    updateAssistantMessageIncremental(message) {
        let messageElement = document.querySelector(`[data-message-id="${message.id}"]`);

        if (!messageElement) {
            // 如果消息元素不存在，创建新的消息
            this.appendMessage(message);
            messageElement = document.querySelector(`[data-message-id="${message.id}"]`);
            if (!messageElement) return;
        }

        const contentElement = messageElement.querySelector('.message-bubble');
        if (!contentElement) return;

        // 使用DocumentFragment来批量更新DOM，减少重排
        const fragment = document.createDocumentFragment();

        // 只更新变化的部分，而不是重新渲染整个内容
        this.updateThinkingSectionOptimized(messageElement, message);
        this.updateToolsSectionOptimized(messageElement, message);
        this.updateMainResponseOptimized(messageElement, message);

        // 平滑滚动到底部
        this.smoothScrollToBottom();
    }

    // 更新思维过程区域
    updateThinkingSection(messageElement, message) {
        const thinkingProcess = message.metadata.thinkingProcess || [];
        if (thinkingProcess.length === 0) return;

        let thinkingSection = messageElement.querySelector('.thinking-section');

        if (!thinkingSection) {
            // 创建思维过程区域
            const contentElement = messageElement.querySelector('.message-bubble');
            thinkingSection = document.createElement('div');
            thinkingSection.className = 'thinking-section';
            thinkingSection.innerHTML = `
                <div class="section-header collapsible" onclick="this.parentElement.classList.toggle('collapsed')">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-brain"></i>
                        <span>思维过程 (${thinkingProcess.length} 步)</span>
                    </div>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="section-content">
                    <div class="thinking-steps"></div>
                </div>
            `;
            contentElement.insertBefore(thinkingSection, contentElement.firstChild);
        }

        // 更新步骤计数
        const headerSpan = thinkingSection.querySelector('.section-header span');
        if (headerSpan) {
            headerSpan.textContent = `思维过程 (${thinkingProcess.length} 步)`;
        }

        // 增量添加新的思维步骤
        const stepsContainer = thinkingSection.querySelector('.thinking-steps');
        const existingSteps = stepsContainer.children.length;

        for (let i = existingSteps; i < thinkingProcess.length; i++) {
            const step = thinkingProcess[i];
            const stepElement = document.createElement('div');
            stepElement.className = 'thinking-step';
            stepElement.innerHTML = this.formatMessageContent(step.content);

            // 添加淡入动画
            stepElement.style.opacity = '0';
            stepElement.style.transform = 'translateY(10px)';
            stepsContainer.appendChild(stepElement);

            // 触发动画
            setTimeout(() => {
                stepElement.style.transition = 'all 0.3s ease';
                stepElement.style.opacity = '1';
                stepElement.style.transform = 'translateY(0)';
            }, 50);
        }
    }

    // 更新工具执行区域
    updateToolsSection(messageElement, message) {
        const toolExecutions = message.metadata.toolExecutions || [];
        if (toolExecutions.length === 0) return;

        let toolsSection = messageElement.querySelector('.tools-section');

        if (!toolsSection) {
            // 创建工具执行区域
            const contentElement = messageElement.querySelector('.message-bubble');
            const thinkingSection = contentElement.querySelector('.thinking-section');

            toolsSection = document.createElement('div');
            toolsSection.className = 'tools-section';
            toolsSection.innerHTML = `
                <div class="section-header collapsible" onclick="this.parentElement.classList.toggle('collapsed')">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-wrench"></i>
                        <span>工具执行 (${toolExecutions.length} 个)</span>
                    </div>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="section-content">
                    <div class="tool-executions"></div>
                </div>
            `;

            if (thinkingSection) {
                thinkingSection.insertAdjacentElement('afterend', toolsSection);
            } else {
                contentElement.insertBefore(toolsSection, contentElement.firstChild);
            }
        }

        // 更新工具计数
        const headerSpan = toolsSection.querySelector('.section-header span');
        if (headerSpan) {
            headerSpan.textContent = `工具执行 (${toolExecutions.length} 个)`;
        }

        // 增量添加新的工具执行
        const executionsContainer = toolsSection.querySelector('.tool-executions');
        const existingExecutions = executionsContainer.children.length;

        for (let i = existingExecutions; i < toolExecutions.length; i++) {
            const tool = toolExecutions[i];
            const toolElement = document.createElement('div');
            toolElement.innerHTML = this.renderToolExecution(tool);

            // 添加淡入动画
            toolElement.style.opacity = '0';
            toolElement.style.transform = 'translateY(10px)';
            executionsContainer.appendChild(toolElement);

            // 触发动画
            setTimeout(() => {
                toolElement.style.transition = 'all 0.3s ease';
                toolElement.style.opacity = '1';
                toolElement.style.transform = 'translateY(0)';
            }, 100);
        }
    }

    // 更新主要回复区域
    updateMainResponse(messageElement, message) {
        if (!message.content) return;

        let mainResponse = messageElement.querySelector('.main-response');

        if (!mainResponse) {
            // 创建主要回复区域
            const contentElement = messageElement.querySelector('.message-bubble');
            mainResponse = document.createElement('div');
            mainResponse.className = 'main-response';
            contentElement.appendChild(mainResponse);
        }

        // 使用打字机效果更新内容
        if (mainResponse.textContent !== message.content) {
            this.typewriterEffect(mainResponse, message.content, 15);
        }
    }

    // 平滑滚动到底部
    smoothScrollToBottom() {
        const messagesContainer = document.getElementById('chat-messages');
        messagesContainer.scrollTo({
            top: messagesContainer.scrollHeight,
            behavior: 'smooth'
        });
    }

    // 优化版本的思维过程更新（避免闪烁）
    updateThinkingSectionOptimized(messageElement, message) {
        const thinkingProcess = message.metadata.thinkingProcess || [];
        const thinkingSection = messageElement.querySelector('[data-role="thinking-section"]');
        if (!thinkingSection) return;

        if (thinkingProcess.length === 0) {
            thinkingSection.style.display = 'none';
            return;
        }
        thinkingSection.style.display = ''; // Show if there are steps

        const headerSpan = thinkingSection.querySelector('[data-role="thinking-count"]');
        if (headerSpan) {
            headerSpan.textContent = `思维过程 (${thinkingProcess.length} 步)`;
        }

        const stepsContainer = thinkingSection.querySelector('[data-role="thinking-steps"]');
        const existingStepsCount = stepsContainer.children.length;

        for (let i = existingStepsCount; i < thinkingProcess.length; i++) {
            const step = thinkingProcess[i];
            const stepElement = this.createThinkingStepElement(step);
            stepsContainer.appendChild(stepElement);
            requestAnimationFrame(() => {
                stepElement.style.opacity = '1';
                stepElement.style.transform = 'translateY(0)';
            });
        }
    }

    // 优化版本的工具执行更新（避免闪烁）
    updateToolsSectionOptimized(messageElement, message) {
        const toolExecutions = message.metadata.toolExecutions || [];
        const toolsSection = messageElement.querySelector('[data-role="tools-section"]');
        if (!toolsSection) return;

        if (toolExecutions.length === 0) {
            toolsSection.style.display = 'none';
            return;
        }
        toolsSection.style.display = ''; // Show if there are executions

        const headerSpan = toolsSection.querySelector('[data-role="tools-count"]');
        if (headerSpan) {
            headerSpan.textContent = `工具执行 (${toolExecutions.length} 个)`;
        }

        const executionsContainer = toolsSection.querySelector('[data-role="tool-executions"]');
        const existingExecutionsCount = executionsContainer.children.length;

        for (let i = existingExecutionsCount; i < toolExecutions.length; i++) {
            const tool = toolExecutions[i];
            const toolElement = this.createToolExecutionElement(tool);
            executionsContainer.appendChild(toolElement);
            requestAnimationFrame(() => {
                toolElement.style.opacity = '1';
                toolElement.style.transform = 'translateY(0)';
            });
        }
    }

    // 优化版本的主要回复更新（避免闪烁）
    updateMainResponseOptimized(messageElement, message) {
        const mainResponse = messageElement.querySelector('[data-role="main-response"]');
        if (!mainResponse) return;

        const loadingDots = mainResponse.querySelector('.loading-dots');

        if (message.content) {
            if (loadingDots) {
                loadingDots.remove();
            }
            this.typewriterEffect(mainResponse, message.content, 15);
        } else {
            // No main content yet.
            if (loadingDots && (message.metadata.thinkingProcess?.length || message.metadata.toolExecutions?.length)) {
                loadingDots.remove();
            }
        }
    }

    // 创建思维步骤元素的辅助方法
    createThinkingStepElement(step) {
        const stepElement = document.createElement('div');
        stepElement.className = 'thinking-step';
        stepElement.innerHTML = this.formatMessageContent(step.content);

        // 设置初始动画状态
        stepElement.style.opacity = '0';
        stepElement.style.transform = 'translateY(10px)';
        stepElement.style.transition = 'all 0.3s ease';

        return stepElement;
    }

    // 创建工具执行元素的辅助方法
    createToolExecutionElement(tool) {
        const toolElement = document.createElement('div');
        toolElement.innerHTML = this.renderToolExecution(tool);

        // 设置初始动画状态
        toolElement.style.opacity = '0';
        toolElement.style.transform = 'translateY(10px)';
        toolElement.style.transition = 'all 0.3s ease';

        return toolElement;
    }

    // 渲染助手消息内容 - 统一气泡设计
    renderAssistantMessageContent(message) {
        // 创建统一的助手消息结构，包含思维过程、工具执行和最终回复
        const hasThinking = message.metadata.thinkingProcess && message.metadata.thinkingProcess.length > 0;
        const hasTools = message.metadata.toolExecutions && message.metadata.toolExecutions.length > 0;
        const hasContent = message.content && message.content.trim().length > 0;

        let html = '';

        // 任务规划现在合并到思维过程中，这里不再单独显示

        // 思维过程区域（可折叠）- 包含规划和执行思维
        const hasPlanning = message.metadata.planningSteps && message.metadata.planningSteps.length > 0;
        const planningStatus = message.metadata.planningStatus || 'idle';
        const planningMessage = message.metadata.planningMessage || '';
        const planningProgress = message.metadata.planningProgress || 0;
        const hasPlanningThoughts = message.metadata.planningThoughts && message.metadata.planningThoughts.length > 0;

        if (hasThinking || hasPlanningThoughts || hasPlanning || message.metadata.isActive) {
            html += `
                <div class="thinking-section ${hasThinking || hasPlanningThoughts || hasPlanning ? '' : 'hidden'} ${message.metadata.isActive ? '' : 'collapsed'}" data-role="thinking-section">
                    <div class="section-header collapsible" onclick="this.parentElement.classList.toggle('collapsed')">
                        <div class="section-title">
                            <i class="fas fa-brain"></i>
                            <span data-role="thinking-count">思维过程</span>
                            ${planningStatus !== 'idle' && planningStatus !== 'completed' ? `
                                <div class="planning-status">
                                    <span class="status-indicator ${planningStatus}"></span>
                                    <span class="status-text">${planningMessage}</span>
                                </div>
                            ` : ''}
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </div>
                    <div class="section-content">
                        ${planningProgress > 0 && planningProgress < 100 ? `
                            <div class="planning-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${planningProgress}%"></div>
                                </div>
                                <span class="progress-text">${Math.round(planningProgress)}%</span>
                            </div>
                        ` : ''}

                        ${hasPlanningThoughts ? `
                            <div class="planning-thoughts-section">
                                <h4><i class="fas fa-lightbulb"></i> 任务规划思维</h4>
                                <div class="planning-thoughts">
                                    ${message.metadata.planningThoughts.map(thought => `
                                        <div class="planning-thought">
                                            <span class="thought-content">${thought.content}</span>
                                            <span class="thought-stage">${thought.stage}</span>
                                        </div>
                                    `).join('')}
                                </div>
                                ${hasPlanning ? `
                                    <div class="planning-result">
                                        <h5><i class="fas fa-list-ol"></i> 生成的执行计划 (${message.metadata.planningSteps.length} 步)</h5>
                                        <div class="planning-steps">
                                            ${this.renderPlanningSteps(message.metadata.planningSteps)}
                                        </div>
                                    </div>
                                ` : ''}
                            </div>
                        ` : ''}

                            <div class="execution-thoughts-section" data-role="thinking-section">

                                <div class="thinking-steps" data-role="thinking-steps">
                                    ${this.renderThinkingSteps(message.metadata.thinkingProcess)}
                                </div>
                            </div>

                    </div>
                </div>
            `;
        }

        // 工具执行区域（可折叠）- 支持流式更新
        const currentExecution = message.metadata.currentExecution;
        html += `
            <div class="tools-section ${hasTools || currentExecution ? '' : 'hidden'}" data-role="tools-section">
                <div class="section-header collapsible" onclick="this.parentElement.classList.toggle('collapsed')">
                    <div class="section-title">
                        <i class="fas fa-wrench"></i>
                        <span data-role="tools-count">工具执行 ${hasTools ? `(${message.metadata.toolExecutions.length} 个)` : ''}</span>
                        ${currentExecution ? `
                            <div class="current-execution">
                                <span class="execution-indicator executing"></span>
                                <span class="execution-text">${currentExecution.message}</span>
                            </div>
                        ` : ''}
                    </div>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="section-content">
                    ${currentExecution && currentExecution.progress ? `
                        <div class="execution-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${currentExecution.progress}%"></div>
                            </div>
                            <span class="progress-text">${Math.round(currentExecution.progress)}%</span>
                        </div>
                    ` : ''}
                    <div class="tool-executions" data-role="tool-executions">
                        ${hasTools ? this.renderToolExecutions(message.metadata.toolExecutions) : ''}
                    </div>
                    ${message.metadata.executionErrors && message.metadata.executionErrors.length > 0 ? `
                        <div class="execution-errors">
                            <h4>执行错误:</h4>
                            ${message.metadata.executionErrors.map(error => `
                                <div class="execution-error">
                                    <span class="error-step">步骤 ${error.step_id}:</span>
                                    <span class="error-message">${error.error}</span>
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        // 模型思考过程区域 (来自 <think> 标签) - COT模型默认展开，可折叠
        const hasLlmThoughts = message.metadata.llmThoughtProcess && message.metadata.llmThoughtProcess.length > 0;
        if (hasLlmThoughts) {
            console.log('[COT] 显示模型思考过程:', message.metadata.llmThoughtProcess.length, '步');
            const llmThoughtsHtml = `
                <div class="llm-thinking-section" data-role="llm-thinking-section">
                    <div class="section-header collapsible" onclick="this.parentElement.classList.toggle('collapsed')">
                        <div class="section-title">
                            <i class="fas fa-comment-dots"></i>
                            <span data-role="llm-thoughts-count">模型内部思考 (${message.metadata.llmThoughtProcess.length} 步)</span>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </div>
                    <div class="section-content">
                        <div class="llm-thought-steps" data-role="llm-thought-steps">
                            ${this.renderLlmThoughtProcess(message.metadata.llmThoughtProcess)}
                        </div>
                    </div>
                </div>
            `;
            html += llmThoughtsHtml;
        }
        // END OF UI Revert

        // 主要回复区域
        html += `
            <div class="main-response" data-role="main-response">
                ${hasContent ?
                    `<div class="response-content">${this.linkifyCitations(this.formatMessageContent(message.content), message.metadata.references)}</div>` :
                    (message.metadata.isActive ? '<div class="loading-dots">正在思考中...</div>' : '')
                }
            </div>
        `;

        // 任务状态区域（折叠显示）
        if (message.metadata.taskSummary) {
            html += `
                <div class="task-status-section collapsed" data-role="task-status">
                    <div class="task-status-header" onclick="this.parentElement.classList.toggle('collapsed')">
                        <span class="task-status-text">${message.metadata.taskSummary}</span>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </div>
                    <div class="task-status-content">
                        <div class="task-details">
                            <p>任务执行详情已在上方展示，包括思维过程和工具执行结果。</p>
                        </div>
                    </div>
                </div>
            `;
        }

        // 参考文献区域
        /* UI Revert: Temporarily commenting out "参考文献区域"
        if (message.metadata.references && message.metadata.references.length > 0) {
console.log('[renderAssistantMessageContent] Rendering references:', message.metadata.references);
            html += this.renderReferences(message.metadata.references);
        }
        */

        return html;
    }

    escapeHtml(unsafe) {
        if (unsafe === null || typeof unsafe === 'undefined') {
            return '';
        }
        return unsafe
            .toString()
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    renderReferences(references) {
        if (!references || references.length === 0) {
            console.log('[renderReferences] No references to render or input is empty.');
            return '';
        }
        console.log('[renderReferences] Input references:', references);
        const itemsHtml = references.map(ref => `
            <li id="ref-${ref.index}">
                <a href="${this.escapeHtml(ref.url)}" target="_blank" title="${this.escapeHtml(ref.url)}">[${ref.index}] ${this.escapeHtml(ref.title)}</a>
            </li>
        `).join('');
console.log('[renderReferences] Generated itemsHtml:', itemsHtml);

        return `
            <div class="references-section" data-role="references-section">
                <div class="section-header">
                    <div class="section-title">
                        <i class="fas fa-book"></i>
                        <span>参考文献</span>
                    </div>
                </div>
                <div class="section-content">
                    <ol class="references-list">
                        ${itemsHtml}
                    </ol>
                </div>
            </div>
        `;
    }

    linkifyCitations(contentHtml, references) {
console.log('[linkifyCitations] Input contentHtml (first 100 chars):', contentHtml ? contentHtml.substring(0,100) : 'null');
    console.log('[linkifyCitations] Input references:', references);
        if (!references || references.length === 0 || !contentHtml) {
            console.log('[linkifyCitations] No references or contentHtml, returning original contentHtml.');
            return contentHtml;
        }
        // Replace [N] with a link to #ref-N
        // Ensure we are not inside an HTML tag when replacing
        const linkedHtml = contentHtml.replace(/\[(\d+)\](?!([^<]*>|[^<>]*<\/))/g, (match, numberStr) => {
            const number = parseInt(numberStr);
            const refExists = references.some(ref => ref.index === number);
            if (refExists) {
                console.log(`[linkifyCitations] Linking citation: [${number}]`);
                return `<a href="#ref-${number}" class="reference-link" title="跳转到参考文献 ${number}">[${number}]</a>`;
            }
            console.log(`[linkifyCitations] Citation [${number}] not found in references, keeping original.`);
            return match; // Return original if no matching reference
        });
        console.log('[linkifyCitations] Output linkedHtml (first 100 chars):', linkedHtml ? linkedHtml.substring(0,100) : 'null');
        return linkedHtml;
    }

    // 渲染规划步骤
    renderPlanningSteps(planningSteps) {
        return planningSteps.map(step => `
            <div class="planning-step">
                <div class="step-header">
                    <span class="step-number">${step.id}</span>
                    <span class="step-tool">${step.tool}.${step.action}</span>
                    <span class="step-status status-${step.status}">${this.getStatusText(step.status)}</span>
                </div>
                <div class="step-description">${this.formatMessageContent(step.description)}</div>
                ${Object.keys(step.parameters || {}).length > 0 ? `
                    <div class="step-parameters">
                        <strong>参数:</strong> ${JSON.stringify(step.parameters, null, 2)}
                    </div>
                ` : ''}
            </div>
        `).join('');
    }

    // 获取状态文本
    getStatusText(status) {
        const statusMap = {
            'pending': '待执行',
            'in_progress': '执行中',
            'completed': '已完成',
            'failed': '失败'
        };
        return statusMap[status] || status;
    }

    // 渲染思维步骤
    renderThinkingSteps(thinkingProcess) {
        return thinkingProcess.map(step => `
            <div class="thinking-step">
                <div class="step-content">${this.formatMessageContent(step.content)}</div>
            </div>
        `).join('');
    }

    renderLlmThoughtProcess(llmThoughts) {
        if (!llmThoughts || llmThoughts.length === 0) {
            return '';
        }
        return llmThoughts.map(step => `
            <div class="llm-thought-step">${this.formatMessageContent(step.content)}</div>
        `).join('');
    }

    // 渲染工具执行列表
    renderToolExecutions(toolExecutions) {
        return toolExecutions.map(tool => this.renderToolExecution(tool)).join('');
    }

    // 渲染工具执行消息
    renderToolExecutionMessage(message, time) {
        const toolExecutions = message.metadata.toolExecutions || [];
        const toolsHtml = toolExecutions.map(tool => this.renderToolExecution(tool)).join('');

        return `
            <div class="message tool-execution">
                <div class="message-avatar"><i class="fas fa-wrench"></i></div>
                <div class="message-content">
                    <div class="message-bubble tool-execution-bubble">
                        <div class="tool-execution-header">
                            <i class="fas fa-cogs"></i>
                            <span>工具执行过程</span>
                        </div>
                        ${toolsHtml}
                    </div>
                    <div class="message-time">${time}</div>
                </div>
            </div>
        `;
    }

    // 渲染思维过程消息
    renderThinkingMessage(message, time) {
        const thinkingSteps = message.metadata.thinkingProcess || [];
        const thinkingHtml = thinkingSteps.map(thought => this.renderThinkingProcess(thought)).join('');

        return `
            <div class="message thinking">
                <div class="message-avatar"><i class="fas fa-brain"></i></div>
                <div class="message-content">
                    <div class="message-bubble thinking-bubble">
                        <div class="thinking-header">
                            <i class="fas fa-lightbulb"></i>
                            <span>思维过程</span>
                        </div>
                        ${thinkingHtml}
                    </div>
                    <div class="message-time">${time}</div>
                </div>
            </div>
        `;
    }

    // 渲染系统消息
    renderSystemMessage(message, time) {
        return `
            <div class="message system">
                <div class="message-content">
                    <div class="message-bubble system-bubble">
                        <i class="fas fa-info-circle"></i>
                        ${this.formatMessageContent(message.content)}
                    </div>
                    <div class="message-time">${time}</div>
                </div>
            </div>
        `;
    }

    // 初始化WebSocket连接
    initializeWebSocket() {
        // 如果已经有连接，先关闭
        if (this.websocket) {
            this.websocket.close();
        }

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;

        console.log('正在连接WebSocket:', wsUrl);
        this.updateModelStatus('connecting');

        this.websocket = new WebSocket(wsUrl);

        this.websocket.onopen = () => {
            console.log('WebSocket连接已建立');
            this.updateModelStatus('active');
            this.showNotification('模型连接成功', 'success');
            // 重置重连计数器
            this.reconnectAttempts = 0;
        };

        this.websocket.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                this.handleWebSocketMessage(message);
            } catch (error) {
                console.error('解析WebSocket消息失败:', error);
            }
        };

        this.websocket.onclose = (event) => {
            console.log('WebSocket连接已关闭, 代码:', event.code, '原因:', event.reason);
            this.updateModelStatus('error');

            // 如果不是手动关闭，尝试重连
            if (event.code !== 1000) {
                this.attemptReconnect();
            }
        };

        this.websocket.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.updateModelStatus('error');
        };
    }

    // 尝试重连
    attemptReconnect() {
        if (!this.reconnectAttempts) {
            this.reconnectAttempts = 0;
        }

        this.reconnectAttempts++;
        const maxAttempts = 5;
        const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts - 1), 30000); // 指数退避，最大30秒

        if (this.reconnectAttempts <= maxAttempts) {
            console.log(`第 ${this.reconnectAttempts} 次重连尝试，${delay/1000}秒后重试...`);
            this.showNotification(`连接断开，${delay/1000}秒后重试 (${this.reconnectAttempts}/${maxAttempts})`, 'warning');

            setTimeout(() => {
                this.initializeWebSocket();
            }, delay);
        } else {
            console.error('WebSocket重连失败，已达到最大重试次数');
            this.showNotification('连接失败，请刷新页面重试', 'error');
        }
    }

    // 处理WebSocket消息
    handleWebSocketMessage(message) {
        // 检查消息是否属于当前会话（如果消息包含session_id）
        if (message.data && message.data.session_id && message.data.session_id !== this.currentSessionId) {
            return;
        }

        switch (message.type) {
            case 'task_start':
                this.handleTaskStart(message.data);
                break;
            case 'planning_start':
                this.handlePlanningStart(message.data);
                break;
            case 'planning_thinking':
                this.handlePlanningThinking(message.data);
                break;
            case 'planning_step_added':
                this.handlePlanningStepAdded(message.data);
                break;
            case 'planning_complete':
                this.handlePlanningComplete(message.data);
                break;
            case 'execution_start':
                this.handleExecutionStart(message.data);
                break;
            case 'step_start':
                this.handleStepStart(message.data);
                break;
            case 'cot_step':
                this.handleCOTStep(message.data);
                break;
            case 'tool_execution_start':
                this.handleToolExecutionStart(message.data);
                break;
            case 'tool_execution_progress':
                this.handleToolExecutionProgress(message.data);
                break;
            case 'tool_call':
                this.handleToolCall(message.data);
                break;
            case 'tool_execution_error':
                this.handleToolExecutionError(message.data);
                break;
            case 'step_complete':
                this.handleStepComplete(message.data);
                break;
            case 'execution_complete':
                this.handleExecutionComplete(message.data);
                break;
            case 'execution_error':
                this.handleExecutionError(message.data);
                break;
            case 'task_error':
                this.handleTaskError(message.data);
                break;
            case 'session_message':
                this.handleSessionMessage(message.data);
                break;
            case 'model_info':
                this.handleModelInfo(message.data);
                break;
            default:
                console.warn('未知的WebSocket消息类型:', message.type);
        }
    }

    // 处理规划完成
    handlePlanningComplete(data) {
        // 如果包含模型信息，更新当前模型
        if (data.model_name && data.model_name !== this.currentModel) {
            this.currentModel = data.model_name;
            this.updateModelStatusDisplay();
        }

        // 处理规划步骤显示
        if (!this.currentSessionId) {
            return;
        }

        const session = this.sessions.get(this.currentSessionId);
        if (!session) {
            return;
        }

        let assistantMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);
        if (!assistantMessage) {
            assistantMessage = this.addMessage('assistant', '', {
                isActive: true,
                toolExecutions: [],
                thinkingProcess: [],
                planningSteps: []
            });
            this.currentAssistantMessageId = assistantMessage.id;
        }

        if (data.steps && data.steps.length > 0) {
            // 添加规划信息到助手消息
            if (!assistantMessage.metadata.planningSteps) {
                assistantMessage.metadata.planningSteps = [];
            }
            assistantMessage.metadata.planningSteps = data.steps;
            assistantMessage.metadata.planId = data.plan_id;

            // 更新消息显示
            this.updateAssistantMessage(assistantMessage);
        }
    }

    // 处理工具调用
    handleToolCall(data) {
        if (!this.currentSessionId) return;

        const session = this.sessions.get(this.currentSessionId);
        let assistantMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);

        if (!assistantMessage) {
            assistantMessage = this.addMessage('assistant', '', {
                isActive: true,
                toolExecutions: [],
                thinkingProcess: [],
                references: [] // 初始化参考文献数组
            });
            this.currentAssistantMessageId = assistantMessage.id;
        } else {
            // 确保元数据存在
            if (!assistantMessage.metadata) {
                assistantMessage.metadata = {};
            }
            if (!assistantMessage.metadata.toolExecutions) {
                assistantMessage.metadata.toolExecutions = [];
            }
            if (!assistantMessage.metadata.references) {
                assistantMessage.metadata.references = [];
            }
        }

        const toolExecution = {
            tool: data.tool,
            action: data.action,
            parameters: data.parameters,
            result: data.result,
            timestamp: data.timestamp,
            status: data.status || 'completed',
            execution_time: data.execution_time
        };

        assistantMessage.metadata.toolExecutions.push(toolExecution);

        // 如果是 web_search 工具，提取参考文献
        if (data.tool === 'web_search' && data.result && typeof data.result === 'string') {
            const references = [];
            // 正则表达式匹配搜索结果条目
            // 序号. 标题\n   来源: ...\n   摘要: ...\n   链接: URL
            const regex = /(\d+)\.\s*(.*?)\n\s*来源:.*?\n\s*摘要:.*?\n\s*链接:\s*(https?:\/\/[^\s]+)/g;
            let match;
            while ((match = regex.exec(data.result)) !== null) {
                references.push({
                    index: parseInt(match[1]),
                    title: match[2].trim(),
                    url: match[3].trim()
                });
            }

            // 也尝试从详细网页内容部分提取（如果格式固定）
            // **网页 1: Title**\n链接: URL
            const pageContentRegex = /\*\*网页\s*(\d+):\s*(.*?)\*\*\n链接:\s*(https?:\/\/[^\s]+)/g;
            while ((match = pageContentRegex.exec(data.result)) !== null) {
                const existingRef = references.find(ref => ref.url === match[3].trim());
                if (!existingRef) { // 避免重复添加相同的URL
                    references.push({
                        index: parseInt(match[1]),
                        title: match[2].trim(),
                        url: match[3].trim()
                    });
                }
            }

            // 对参考文献按 index 排序并去重（基于URL）
            const uniqueReferences = Array.from(new Map(references.map(ref => [ref.url, ref])).values())
                                       .sort((a, b) => a.index - b.index);

            // 重新编号，确保序号从1开始连续
            assistantMessage.metadata.references = uniqueReferences.map((ref, idx) => ({
                ...ref,
                originalIndex: ref.index, // 保留原始解析的序号
                index: idx + 1 // 新的连续序号
            }));
        }

        this.updateAssistantMessage(assistantMessage);
    }

    // 处理COT步骤
    handleCOTStep(data) {
        if (!this.currentSessionId) return;

        const session = this.sessions.get(this.currentSessionId);

        // 使用 currentAssistantMessageId 获取助手消息
        let assistantMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);

        if (!assistantMessage) {
             // 如果找不到（异常情况），记录错误并尝试创建一个新的
            console.error("Error: currentAssistantMessageId not found for cot_step. Creating new message.");
            assistantMessage = this.addMessage('assistant', '', {
                isActive: true, // 保持isActive
                toolExecutions: [],
                thinkingProcess: []
            });
            this.currentAssistantMessageId = assistantMessage.id; // 更新ID
        }

        // 添加思维步骤
        assistantMessage.metadata.thinkingProcess.push({
            content: data.content,
            timestamp: data.timestamp
        });

        // 使用流畅更新，避免闪烁
        this.updateAssistantMessage(assistantMessage);
    }

    // 处理执行完成
    async handleExecutionComplete(data) {
        this.hideTypingIndicator();
        this.isProcessing = false;
        this.handleInputChange();

        // 如果包含模型信息，更新当前模型
        if (data.model_name && data.model_name !== this.currentModel) {
            this.currentModel = data.model_name;
            this.updateModelStatusDisplay();
        }

        if (!this.currentSessionId) return;

        const session = this.sessions.get(this.currentSessionId);

        // 使用 currentAssistantMessageId 获取助手消息
        let assistantMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);

        if (assistantMessage) {
            // 标记为完成
            assistantMessage.metadata.isActive = false;

            // 保存模型信息到消息元数据
            if (data.model_name) {
                assistantMessage.metadata.model_name = data.model_name;
            }

            // 添加简洁的任务状态总结
            if (data.summary) {
                assistantMessage.metadata.taskSummary = data.summary;
            }

            // 使用智能回复（如果有）或生成用户友好的回答
            let finalAnswer;
            if (data.intelligent_response) {
                finalAnswer = data.intelligent_response;
            } else {
                finalAnswer = this.generateUserFriendlyAnswer(assistantMessage);
            }

            // 处理think标签（仅对COT模型）
            const processedAnswer = this.processThinkContent(finalAnswer);

            if (typeof processedAnswer === 'object' && processedAnswer.thinkingSteps && processedAnswer.thinkingSteps.length > 0) {
                // LLM response included <think> tags
                assistantMessage.content = processedAnswer.content; // Content without <think> tags

                // Store LLM's <think> steps in a new field: llmThoughtProcess
                if (!assistantMessage.metadata.llmThoughtProcess) {
                    assistantMessage.metadata.llmThoughtProcess = [];
                }
                assistantMessage.metadata.llmThoughtProcess = processedAnswer.thinkingSteps;
            } else {
                // No <think> tags in LLM response, or processThinkContent returned a string
                // In this case, processedAnswer is the original finalAnswer string
                assistantMessage.content = processedAnswer;
            }

            assistantMessage.metadata.finalSummarySet = true;

            // 使用流畅更新显示最终回复
            this.updateAssistantMessage(assistantMessage);

            this.currentAssistantMessageId = null; // 清空当前活跃助手消息ID
        } else {
            console.error("Error: currentAssistantMessageId not found for execution_complete.");
            this.addMessage('assistant', data.intelligent_response || data.summary || "执行完成，但无法更新原始消息。");
            this.currentAssistantMessageId = null;
        }
    }

    // 处理会话消息 (例如最终总结) - 统一到同一个气泡
    handleSessionMessage(data) {
        if (!this.currentSessionId) return;
        const session = this.sessions.get(this.currentSessionId);

        // 如果有活跃的助手消息，将总结添加到同一个气泡中
        if (this.currentAssistantMessageId) {
            let assistantMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);
            if (assistantMessage) {
                // 将总结内容添加到同一个消息中
                const processed = this.processThinkContent(data.content);
                if (typeof processed === 'object') {
                    assistantMessage.content = processed.content;
                    if (!assistantMessage.metadata.llmThoughtProcess) assistantMessage.metadata.llmThoughtProcess = [];
                    assistantMessage.metadata.llmThoughtProcess = assistantMessage.metadata.llmThoughtProcess.concat(processed.thinkingSteps);
                } else {
                    assistantMessage.content = processed;
                }
                assistantMessage.metadata.isActive = false;
                assistantMessage.metadata.finalSummarySet = true;

                // 使用流畅更新显示最终内容
                this.updateAssistantMessage(assistantMessage);

                // 清理状态
                this.hideTypingIndicator();
                this.isProcessing = false;
                this.handleInputChange();
                this.currentAssistantMessageId = null;
                return;
            }
        }

        // 如果没有活跃的助手消息，创建新的（这种情况应该很少发生）
        let finalContent = data.content;
        let llmThoughts = [];
        const processedNew = this.processThinkContent(data.content);
        if (typeof processedNew === 'object') {
            finalContent = processedNew.content;
            llmThoughts = processedNew.thinkingSteps;
        }
        const newMessage = this.addMessage(data.role || 'assistant', finalContent, { llmThoughtProcess: llmThoughts });
        if (data.is_final_summary) {
            const msgToFinalize = session.messages.find(m => m.id === newMessage.id);
            if (msgToFinalize) {
                msgToFinalize.metadata.isActive = false;
                msgToFinalize.metadata.finalSummarySet = true;
            }
        }

        this.hideTypingIndicator();
        this.isProcessing = false;
        this.handleInputChange();
    }

    // 处理步骤开始
    handleStepStart(data) {
        if (!this.currentSessionId) return;
        const session = this.sessions.get(this.currentSessionId);
        let assistantMessage = session.messages.find(m => m.role === 'assistant' && m.metadata.isActive);

        if (assistantMessage) {
            // 更新 metadata.steps（原有逻辑）
            if (!assistantMessage.metadata.steps) {
                assistantMessage.metadata.steps = {};
            }
            assistantMessage.metadata.steps[data.step_id] = {
                status: data.status,
                progress: data.progress
            };

            // 更新 planningSteps 中对应步骤的状态
            if (assistantMessage.metadata.planningSteps) {
                const stepToUpdate = assistantMessage.metadata.planningSteps.find(step => step.id === data.step_id);
                if (stepToUpdate) {
                    stepToUpdate.status = data.status;
                    // 更新UI显示
                    this.updateAssistantMessage(assistantMessage);
                }
            }
        }
    }

    // 处理步骤完成
    handleStepComplete(data) {
        if (!this.currentSessionId) return;
        const session = this.sessions.get(this.currentSessionId);
        let assistantMessage = session.messages.find(m => m.role === 'assistant' && m.metadata.isActive);

        if (assistantMessage) {
            // 更新 metadata.steps（原有逻辑）
            if (!assistantMessage.metadata.steps) {
                assistantMessage.metadata.steps = {};
            }
            assistantMessage.metadata.steps[data.step_id] = {
                status: data.status,
                result: data.result,
                progress: data.progress
            };

            // 更新 planningSteps 中对应步骤的状态
            if (assistantMessage.metadata.planningSteps) {
                const stepToUpdate = assistantMessage.metadata.planningSteps.find(step => step.id === data.step_id);
                if (stepToUpdate) {
                    stepToUpdate.status = data.status;
                    if (data.result) {
                        stepToUpdate.result = data.result;
                    }
                    // 更新UI显示
                    this.updateAssistantMessage(assistantMessage);
                }
            }
        }
    }

    // 处理模型信息
    handleModelInfo(data) {
        if (data.model_name && data.model_name !== this.currentModel) {
            this.currentModel = data.model_name;
            this.updateModelStatusDisplay();
        }
    }

    // 处理执行开始
    handleExecutionStart(data) {
        // 可以在这里更新UI，例如显示执行状态
    }

    // 生成简洁的总结
    generateSimpleSummary(message) {
        const toolCount = message.metadata.toolExecutions ? message.metadata.toolExecutions.length : 0;
        const thinkingCount = message.metadata.thinkingProcess ? message.metadata.thinkingProcess.length : 0;

        let summary = "✅ **任务执行完成！**\n\n";

        if (thinkingCount > 0) {
            summary += `🧠 经过 ${thinkingCount} 步思考分析\n`;
        }

        if (toolCount > 0) {
            summary += `🔧 成功执行了 ${toolCount} 个工具操作\n`;
        }

        summary += "\n所有执行细节已在上方展示，您可以点击相应区域查看详情。如需进一步帮助，请随时告诉我！";

        return summary;
    }

    // 生成详细的总结
    generateDetailedSummary(message) {
        const toolExecutions = message.metadata.toolExecutions || [];
        const thinkingProcess = message.metadata.thinkingProcess || [];

        let summary = "✅ **任务执行完成！**\n\n";

        // 添加执行概览
        summary += "📊 **执行概览:**\n";
        summary += `• 思维分析步骤: ${thinkingProcess.length} 步\n`;
        summary += `• 工具执行操作: ${toolExecutions.length} 个\n`;
        summary += `• 总执行时间: ${this.calculateTotalExecutionTime(toolExecutions)}\n\n`;

        // 添加主要成果
        if (toolExecutions.length > 0) {
            summary += "🎯 **主要成果:**\n";
            toolExecutions.forEach((tool, index) => {
                const toolName = this.getToolDisplayName(tool.tool);
                const actionName = this.getActionDisplayName(tool.action);
                summary += `${index + 1}. **${toolName}** - ${actionName}\n`;

                // 提取关键结果信息
                const keyResult = this.extractKeyResult(tool.result);
                if (keyResult) {
                    summary += `   ${keyResult}\n`;
                }
            });
            summary += "\n";
        }

        // 添加详细信息提示
        summary += "💡 **查看详情:**\n";
        summary += "• 点击上方「思维过程」查看AI的分析思路\n";
        summary += "• 点击上方「工具执行」查看详细的操作结果\n";
        summary += "• 所有执行数据已保存，可随时回顾\n\n";

        summary += "如果您需要进一步的帮助或有其他问题，请随时告诉我！";

        return summary;
    }

    // 生成用户友好的回答
    generateUserFriendlyAnswer(message) {
        const toolExecutions = message.metadata.toolExecutions || [];
        const thinkingProcess = message.metadata.thinkingProcess || [];

        // 根据工具类型生成不同的回答
        if (toolExecutions.length > 0) {
            const firstTool = toolExecutions[0];

            if (firstTool.tool === 'calculator' || firstTool.tool === 'CalculatorTool') {
                return this.generateCalculatorAnswer(firstTool, message);
            } else if (firstTool.tool === 'WebSearchTool') {
                return this.generateSearchAnswer(firstTool, message);
            } else if (firstTool.tool === 'FileOperationsTool') {
                return this.generateFileAnswer(firstTool, message);
            }
        }

        // 默认回答 - 更丰富的总结
        const taskSummary = message.metadata.taskSummary || '✅ 任务完成';
        return `${taskSummary}

我已经成功完成了您的任务！在执行过程中：

🔧 **执行了 ${toolExecutions.length} 个操作**
📊 **所有步骤均已完成**
⏱️ **处理时间约 ${this.calculateTotalExecutionTime(toolExecutions)}**

您可以点击上方的"思维过程"和"工具执行"查看详细的执行步骤和结果。如果您需要进一步的帮助或有其他问题，请随时告诉我！`;
    }

    // 生成计算器回答
    generateCalculatorAnswer(toolExecution, message) {
        const expression = toolExecution.parameters?.expression || '';

        // 尝试从结果中提取实际答案
        const result = toolExecution.result || '';
        const answerMatch = result.match(/答案:\s*([0-9.+-]+)/);
        const resultMatch = result.match(/结果:\s*\*\*([0-9.+-]+)\*\*/);

        let answer = null;
        if (answerMatch) {
            answer = answerMatch[1];
        } else if (resultMatch) {
            answer = resultMatch[1];
        }

        if (answer) {
            return `${expression} 的计算结果是 **${answer}**。

${message.metadata.taskSummary || '✅ 计算完成'}`;
        } else {
            return `我已经完成了 ${expression} 的计算。您可以在上方的工具执行结果中查看详细信息。

${message.metadata.taskSummary || '✅ 计算完成'}`;
        }
    }

    // 生成搜索回答
    generateSearchAnswer(toolExecution, message) {
        const query = toolExecution.parameters?.query || '';
        const result = toolExecution.result || '';

        // 尝试从结果中提取搜索统计信息
        const resultCountMatch = result.match(/找到结果:\s*(\d+)\s*个/);
        const resultCount = resultCountMatch ? resultCountMatch[1] : '多个';

        // 提取AI分析总结
        const aiAnalysisMatch = result.match(/🤖 \*\*AI分析总结:\*\*([\s\S]*?)(?=\*\*📚|$)/);
        const aiAnalysis = aiAnalysisMatch ? aiAnalysisMatch[1].trim() : '';

        // 提取参考文献
        const referencesMatch = result.match(/📖 参考文献:\*\*([\s\S]*?)$/);
        const references = referencesMatch ? referencesMatch[1].trim() : '';

        // 提取第一个搜索结果作为示例
        const firstResultMatch = result.match(/\*\*1\.\s*([^*]+)\*\*/);
        const firstResult = firstResultMatch ? firstResultMatch[1] : '';

        let answer = `我为您搜索了"${query}"，找到了 ${resultCount} 个高质量的学习资源。`;

        if (firstResult) {
            answer += `\n\n🔥 **推荐首选：** ${firstResult}`;
        }

        // 添加AI分析摘要
        if (aiAnalysis) {
            const analysisPreview = aiAnalysis.substring(0, 200) + (aiAnalysis.length > 200 ? '...' : '');
            answer += `\n\n🤖 **AI学习建议：**\n${analysisPreview}`;
        }

        // 添加参考文献预览
        if (references) {
            const refLines = references.split('\n').slice(0, 3); // 显示前3个参考文献
            answer += `\n\n📖 **主要参考资源：**\n${refLines.join('\n')}`;
            if (references.split('\n').length > 3) {
                answer += '\n...更多资源请查看工具执行详情';
            }
        }

        answer += `\n\n💡 **完整内容：** 点击上方"工具执行"查看详细的AI分析、学习路径建议和完整参考文献列表。

${message.metadata.taskSummary || '✅ 搜索完成'}`;

        return answer;
    }

    // 生成文件操作回答
    generateFileAnswer(toolExecution, message) {
        const action = toolExecution.action || '';
        const filename = toolExecution.parameters?.filename || '';

        if (action === 'create_file' && filename) {
            return `我已经成功创建了文件 "${filename}"。您可以在上方查看文件创建的详细信息。

${message.metadata.taskSummary || '✅ 文件创建完成'}`;
        }

        return `文件操作已完成。您可以在上方查看操作详情。

${message.metadata.taskSummary || '✅ 操作完成'}`;
    }

    // 计算总执行时间
    calculateTotalExecutionTime(toolExecutions) {
        if (toolExecutions.length === 0) return "0秒";

        // 模拟计算执行时间
        const totalSeconds = toolExecutions.length * 2.5; // 假设每个工具平均2.5秒

        if (totalSeconds < 60) {
            return `${totalSeconds.toFixed(1)}秒`;
        } else {
            const minutes = Math.floor(totalSeconds / 60);
            const seconds = (totalSeconds % 60).toFixed(1);
            return `${minutes}分${seconds}秒`;
        }
    }

    // 获取工具显示名称
    getToolDisplayName(toolName) {
        const displayNames = {
            'WebSearchTool': '网络搜索',
            'CalculatorTool': '数学计算',
            'FileOperationsTool': '文件操作',
            'GeneralTool': '通用分析'
        };
        return displayNames[toolName] || toolName;
    }

    // 获取操作显示名称
    getActionDisplayName(actionName) {
        const displayNames = {
            'search': '搜索查询',
            'web_search': '网页搜索',
            'calculate': '数值计算',
            'create_file': '创建文件',
            'analyze': '智能分析'
        };
        return displayNames[actionName] || actionName;
    }

    // 提取关键结果信息
    extractKeyResult(result) {
        if (!result) return null;

        // 尝试提取结果中的关键信息
        const lines = result.split('\n');
        for (const line of lines) {
            if (line.includes('找到结果') || line.includes('计算结果') ||
                line.includes('文件创建') || line.includes('分析完成')) {
                return `   ${line.trim()}`;
            }
        }

        // 如果没有找到特定模式，返回前50个字符
        const cleanResult = result.replace(/[*#`]/g, '').trim();
        return cleanResult.length > 50 ?
            `   ${cleanResult.substring(0, 50)}...` :
            `   ${cleanResult}`;
    }

    // 生成执行总结
    generateExecutionSummary() {
        if (!this.currentSessionId) return "任务执行完成！";

        const session = this.sessions.get(this.currentSessionId);
        const toolMessages = session.messages.filter(m => m.role === 'tool-execution');
        const thinkingMessages = session.messages.filter(m => m.role === 'thinking');

        let summary = "✅ 任务执行完成！\n\n";

        // 添加思维过程总结
        if (thinkingMessages.length > 0) {
            summary += "🧠 **思维过程:**\n";
            thinkingMessages.forEach((msg, index) => {
                if (msg.metadata.thinkingProcess) {
                    msg.metadata.thinkingProcess.forEach(thought => {
                        summary += `• ${thought.content}\n`;
                    });
                }
            });
            summary += "\n";
        }

        // 添加工具执行总结
        if (toolMessages.length > 0) {
            summary += "🔧 **执行结果:**\n";
            toolMessages.forEach((msg, index) => {
                if (msg.metadata.toolExecutions) {
                    msg.metadata.toolExecutions.forEach(tool => {
                        summary += `**${tool.tool}** (${tool.action}):\n`;
                        summary += `${tool.result}\n\n`;
                    });
                }
            });
        }

        summary += "如果您需要进一步的帮助，请随时告诉我！";

        return summary;
    }

    // 处理执行错误
    handleExecutionError(data) {
        this.hideTypingIndicator();
        this.isProcessing = false;
        this.handleInputChange();

        this.addMessage('assistant', `执行过程中出现错误：${data.error}`);
    }

    // 渲染工具执行详情
    renderToolExecution(tool) {
        const time = new Date(tool.timestamp).toLocaleTimeString();

        const toolDisplayName = this.getToolDisplayName(tool.tool || tool.tool_name); // Handle both possible property names
        const toolStatusText = this.getStatusText(tool.status || 'completed');
        const toolStatusClass = (tool.status || 'completed').toLowerCase();

        const toolInputHtml = tool.parameters ? `
            <div class="tool-action">
                <strong>🎯 操作:</strong> ${tool.action || 'N/A'}
            </div>
            <div class="tool-params">
                <strong>📝 参数:</strong>
                <pre><code>${this.escapeHtml(JSON.stringify(tool.parameters, null, 2))}</code></pre>
            </div>` : '';

        const resultBlockHtml = tool.result ? `
            <div class="tool-result">
                <strong>📊 执行结果:</strong>
                <div class="result-content">${this.formatMessageContent(tool.result)}</div>
            </div>
        ` : `<div class="tool-result"><strong>📊 执行结果:</strong><div class="result-content">无结果</div></div>`;

        const executionTimeHtml = `
            <div class="tool-time" style="margin-top: var(--spacing-sm);">
                ⏱️ 执行时间: ${time}
                ${tool.execution_time ? ` | 耗时: ${tool.execution_time}` : ''}
            </div>`;

        const renderedHtml = `
            <div class="tool-execution" data-tool-id="${tool.id || tool.tool_id || ''}">
                <div class="tool-collapsible-section tool-execution-details">
                    <div class="tool-collapsible-header tool-execution-main-header">
                        <div class="tool-header-content">
                            <span class="tool-name">
                                <i class="fas fa-wrench"></i> ${toolDisplayName}
                            </span>
                            <span class="tool-status ${toolStatusClass}">${toolStatusText}</span>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </div>
                    <div class="tool-collapsible-content">
                        ${toolInputHtml}
                        ${!tool.result ? executionTimeHtml : ''}
                    </div>
                </div>
                ${tool.result || !tool.parameters ? `
                <div class="tool-collapsible-section tool-execution-result collapsed">
                    <div class="tool-collapsible-header tool-result-header">
                        <span>执行结果</span>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </div>
                    <div class="tool-collapsible-content">
                        ${resultBlockHtml}
                        ${executionTimeHtml}
                    </div>
                </div>` : ''}
            </div>
        `;

        console.log('🎨 渲染的HTML长度:', renderedHtml.length);
        console.log('🎨 HTML预览:', renderedHtml.substring(0, 200) + '...');
        return renderedHtml;
    }

    // 渲染思维过程
    renderThinkingProcess(thought) {
        return `
            <div class="thinking-process">
                <div class="thinking-header">
                    <i class="fas fa-brain"></i>
                    <span>思维过程</span>
                </div>
                <div class="thinking-content">${thought.content}</div>
            </div>
        `;
    }

    // 获取状态文本
    getStatusText(status) {
        const statusMap = {
            'completed': '✅ 已完成',
            'running': '🔄 执行中',
            'failed': '❌ 失败',
            'pending': '⏳ 等待中'
        };
        return statusMap[status] || '✅ 已完成';
    }

    // 格式化消息内容
    formatMessageContent(content) {
        // 支持基本的Markdown格式
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 粗体
            .replace(/\*(.*?)\*/g, '<em>$1</em>') // 斜体
            .replace(/`(.*?)`/g, '<code>$1</code>') // 行内代码
            .replace(/\n/g, '<br>'); // 换行
    }

    // 格式化时间
    formatTime(date) {
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return `${Math.floor(diff / 60000)}分钟前`;
        } else if (diff < 86400000) { // 24小时内
            return `${Math.floor(diff / 3600000)}小时前`;
        } else {
            return date.toLocaleDateString();
        }
    }

    // 显示欢迎消息
    showWelcomeMessage() {
        const messagesContainer = document.getElementById('chat-messages');
        messagesContainer.innerHTML = `
            <div class="welcome-message">
                <div class="welcome-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <h3>欢迎使用 Agent 智能助手</h3>
                <p>我可以帮您执行各种任务，包括网络搜索、文件操作、数学计算等。请告诉我您需要什么帮助。</p>
                <div class="example-tasks">
                    <button class="example-task" data-task="搜索Python编程教程">搜索Python教程</button>
                    <button class="example-task" data-task="计算144的平方根">数学计算</button>
                    <button class="example-task" data-task="创建一个包含今天日期的文件">创建文件</button>
                    <button class="example-task" data-task="分析当前天气情况">天气分析</button>
                </div>
            </div>
        `;
    }

    // 隐藏欢迎消息
    hideWelcomeMessage() {
        const welcomeMessage = document.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.style.display = 'none';
        }
    }

    // 显示打字指示器
    showTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        indicator.classList.add('show');
    }

    // 隐藏打字指示器
    hideTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        indicator.classList.remove('show');
    }

    // 更新模型状态
    updateModelStatus(status) {
        const modelStatus = document.getElementById('model-status');
        const statusDot = modelStatus.querySelector('.status-dot');
        const tooltip = document.getElementById('model-tooltip');

        statusDot.className = `status-dot ${status}`;

        const statusInfo = {
            'active': {
                text: '已连接',
                tooltip: `模型 ${this.getModelDisplayName(this.currentModel)} 已连接\n• WebSocket连接正常\n• 可以开始对话`
            },
            'connecting': {
                text: '连接中',
                tooltip: '正在连接到模型服务器...\n• 建立WebSocket连接\n• 初始化模型'
            },
            'error': {
                text: '连接失败',
                tooltip: '无法连接到模型服务器\n• 检查网络连接\n• 确认服务器状态\n• 将自动重试连接'
            }
        };

        const info = statusInfo[status];
        modelStatus.querySelector('span').textContent = `模型状态 - ${info.text}`;
        if (tooltip) {
            tooltip.textContent = info.tooltip;
        }
    }

    // 更新工具状态
    updateToolsStatus(tools) {
        const toolsStatus = document.getElementById('tools-status');
        const statusDot = toolsStatus.querySelector('.status-dot');
        const tooltip = document.getElementById('tools-tooltip');

        if (tools && tools.length > 0) {
            statusDot.className = 'status-dot active';
            const toolNames = tools.map(tool => tool.name || tool).join(', ');
            const tooltipText = `工具已就绪 (${tools.length}个)\n• ${toolNames}\n• 可以执行各种任务`;
            if (tooltip) {
                tooltip.textContent = tooltipText;
            }
        } else {
            statusDot.className = 'status-dot error';
            if (tooltip) {
                tooltip.textContent = '工具加载失败\n• 无可用工具\n• 功能受限';
            }
        }
    }

    // 加载系统状态
    async loadSystemStatus() {
        try {
            const response = await fetch('/api/status');
            const status = await response.json();

            this.updateModelStatus(status.model_status);

            // 加载工具信息
            const toolsResponse = await fetch('/api/tools');
            const tools = await toolsResponse.json();
            console.log('可用工具:', tools);

            this.updateToolsStatus(tools);

        } catch (error) {
            console.error('加载系统状态失败:', error);
            this.updateModelStatus('error');
            this.updateToolsStatus([]);
        }
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const container = document.getElementById('notification-container');
        const notification = document.createElement('div');

        notification.className = `notification ${type}`;
        notification.textContent = message;

        container.appendChild(notification);

        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 打字机效果（优化版本，避免闪烁）
    typewriterEffect(element, text, speed = 30) {
        return new Promise((resolve) => {
            // 停止之前的打字机效果
            this.stopTypewriter(element);

            const formattedText = this.formatMessageContent(text);

            // 创建临时元素来获取纯文本内容
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = formattedText;
            const textContent = tempDiv.textContent || tempDiv.innerText || '';

            // 如果文本很短，直接显示
            if (textContent.length < 20) {
                element.innerHTML = formattedText;
                resolve();
                return;
            }

            let i = 0;
            element.innerHTML = '';

            // 创建一个光标元素
            const cursor = document.createElement('span');
            cursor.className = 'typewriter-cursor';
            cursor.textContent = '|';
            cursor.style.animation = 'blink 1s infinite';

            const typeInterval = setInterval(() => {
                if (i < textContent.length) {
                    // 逐字符添加
                    const currentText = textContent.substring(0, i + 1);
                    const formattedCurrentText = this.formatMessageContent(currentText);

                    // 更新内容但保持光标
                    element.innerHTML = formattedCurrentText;
                    element.appendChild(cursor);

                    i++;

                    // 平滑滚动到底部
                    this.smoothScrollToBottom();
                } else {
                    clearInterval(typeInterval);
                    // 移除光标，显示最终格式化文本
                    element.innerHTML = formattedText;
                    this.typewriterIntervals.delete(element);
                    resolve();
                }
            }, speed);

            // 存储定时器以便可以取消
            this.typewriterIntervals.set(element, typeInterval);
        });
    }

    // 停止打字机效果
    stopTypewriter(element) {
        const interval = this.typewriterIntervals.get(element);
        if (interval) {
            clearInterval(interval);
            this.typewriterIntervals.delete(element);
        }
    }

    // 添加打字机消息
    async addTypewriterMessage(role, content, metadata = {}) {
        if (!this.currentSessionId) return;

        const session = this.sessions.get(this.currentSessionId);
        const message = {
            id: ++this.messageId,
            role: role,
            content: '', // 先设为空，打字机效果会逐步填充
            timestamp: new Date(),
            metadata: metadata
        };

        session.messages.push(message);
        session.lastActivity = new Date();

        this.updateSessionsList();
        this.updateChatHeader();

        // 渲染消息但内容为空
        this.renderMessages();

        if (role === 'assistant') {
            // 找到刚添加的消息元素
            const messageElements = document.querySelectorAll('.message.assistant .message-bubble');
            const lastMessageElement = messageElements[messageElements.length - 1];

            if (lastMessageElement) {
                // 应用打字机效果
                await this.typewriterEffect(lastMessageElement, content, 20);
                // 更新消息内容
                message.content = content;
            }
        }

        return message;
    }

    // 初始化设置界面
    initSettings() {
        const settingsBtn = document.getElementById('settings-btn');
        const settingsOverlay = document.getElementById('settings-overlay');
        const closeSettingsBtn = document.getElementById('close-settings');
        const saveSettingsBtn = document.getElementById('save-settings');
        const resetSettingsBtn = document.getElementById('reset-settings');

        // 打开设置
        settingsBtn.addEventListener('click', () => {
            this.openSettings();
        });

        // 关闭设置
        closeSettingsBtn.addEventListener('click', () => {
            this.closeSettings();
        });

        // 点击遮罩关闭
        settingsOverlay.addEventListener('click', (e) => {
            if (e.target === settingsOverlay) {
                this.closeSettings();
            }
        });

        // 保存设置
        saveSettingsBtn.addEventListener('click', () => {
            this.saveSettings();
        });

        // 重置设置
        resetSettingsBtn.addEventListener('click', () => {
            this.resetSettings();
        });

        // 加载保存的设置
        this.loadSettings();
    }

    // 打开设置界面
    openSettings() {
        const settingsOverlay = document.getElementById('settings-overlay');
        settingsOverlay.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    // 关闭设置界面
    closeSettings() {
        const settingsOverlay = document.getElementById('settings-overlay');
        settingsOverlay.classList.remove('show');
        document.body.style.overflow = '';
    }

    // 保存设置
    saveSettings() {
        const settings = {
            modelName: document.getElementById('model-name')?.value || 'qwen3:32b',
            ollamaUrl: document.getElementById('ollama-url')?.value || 'http://10.0.0.6:11434',
            maxTokens: document.getElementById('max-tokens')?.value || '4096',
            temperature: document.getElementById('temperature')?.value || '0.7',
            enabledTools: {
                webSearch: document.getElementById('tool-web-search')?.checked !== false,
                calculator: document.getElementById('tool-calculator')?.checked !== false,
                fileOps: document.getElementById('tool-file-ops')?.checked !== false,
                webFetch: document.getElementById('tool-web-fetch')?.checked !== false
            },
            theme: document.getElementById('theme-select')?.value || 'light',
            fontSize: document.getElementById('font-size')?.value || 'medium'
        };

        // 保存到localStorage
        localStorage.setItem('agentSettings', JSON.stringify(settings));

        // 应用设置
        this.applySettings(settings);

        // 显示保存成功通知
        this.showNotification('设置已保存', 'success');

        // 关闭设置界面
        this.closeSettings();
    }

    // 加载设置
    loadSettings() {
        const savedSettings = localStorage.getItem('agentSettings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            this.applySettingsToUI(settings);
            this.applySettings(settings);
        }
    }

    // 应用设置到UI
    applySettingsToUI(settings) {
        if (document.getElementById('model-name')) {
            document.getElementById('model-name').value = settings.modelName || 'qwen3:32b';
        }
        if (document.getElementById('ollama-url')) {
            document.getElementById('ollama-url').value = settings.ollamaUrl || 'http://10.0.0.6:11434';
        }
        if (document.getElementById('max-tokens')) {
            document.getElementById('max-tokens').value = settings.maxTokens || '4096';
        }
        if (document.getElementById('temperature')) {
            document.getElementById('temperature').value = settings.temperature || '0.7';
            const rangeValue = document.querySelector('.range-value');
            if (rangeValue) rangeValue.textContent = settings.temperature || '0.7';
        }

        if (settings.enabledTools) {
            if (document.getElementById('tool-web-search')) {
                document.getElementById('tool-web-search').checked = settings.enabledTools.webSearch !== false;
            }
            if (document.getElementById('tool-calculator')) {
                document.getElementById('tool-calculator').checked = settings.enabledTools.calculator !== false;
            }
            if (document.getElementById('tool-file-ops')) {
                document.getElementById('tool-file-ops').checked = settings.enabledTools.fileOps !== false;
            }
            if (document.getElementById('tool-web-fetch')) {
                document.getElementById('tool-web-fetch').checked = settings.enabledTools.webFetch !== false;
            }
        }

        if (document.getElementById('theme-select')) {
            document.getElementById('theme-select').value = settings.theme || 'light';
        }
        if (document.getElementById('font-size')) {
            document.getElementById('font-size').value = settings.fontSize || 'medium';
        }
    }

    // 应用设置
    applySettings(settings) {
        // 应用主题
        if (settings.theme) {
            this.applyTheme(settings.theme);
        }

        // 应用字体大小
        if (settings.fontSize) {
            this.applyFontSize(settings.fontSize);
        }

        // 更新当前模型
        if (settings.modelName) {
            this.currentModel = settings.modelName;
        }
    }

    // 应用主题
    applyTheme(theme) {
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        if (theme !== 'light') {
            document.body.classList.add(`theme-${theme}`);
        }
    }

    // 应用字体大小
    applyFontSize(fontSize) {
        document.body.className = document.body.className.replace(/font-size-\w+/g, '');
        if (fontSize !== 'medium') {
            document.body.classList.add(`font-size-${fontSize}`);
        }
    }

    // 重置设置
    resetSettings() {
        if (confirm('确定要重置所有设置到默认值吗？')) {
            localStorage.removeItem('agentSettings');
            this.showNotification('设置已重置为默认值', 'info');
            this.closeSettings();
        }
    }

    // 新增流式处理函数
    handleTaskStart(data) {
        console.log('任务开始:', data);
        // 可以在这里显示任务开始的通知
        this.showNotification(`开始处理任务: ${data.task_description}`, 'info');
    }

    handlePlanningStart(data) {
        console.log('规划开始:', data);
        if (!this.currentSessionId) return;

        const session = this.sessions.get(this.currentSessionId);
        let assistantMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);

        if (assistantMessage) {
            // 添加规划开始状态
            assistantMessage.metadata.planningStatus = 'thinking';
            assistantMessage.metadata.planningMessage = data.message;
            this.updateAssistantMessage(assistantMessage);
        }
    }

    handlePlanningThinking(data) {
        console.log('规划思维:', data);
        if (!this.currentSessionId) return;

        const session = this.sessions.get(this.currentSessionId);
        let assistantMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);

        if (assistantMessage) {
            // 添加思维过程到规划思维中
            if (!assistantMessage.metadata.planningThoughts) {
                assistantMessage.metadata.planningThoughts = [];
            }
            assistantMessage.metadata.planningThoughts.push({
                content: data.message,
                stage: data.stage,
                progress: data.progress,
                timestamp: Date.now()
            });

            // 更新当前规划状态
            assistantMessage.metadata.planningStatus = data.stage;
            assistantMessage.metadata.planningMessage = data.message;
            assistantMessage.metadata.planningProgress = data.progress;

            this.updateAssistantMessage(assistantMessage);
        }
    }

    handlePlanningStepAdded(data) {
        console.log('规划步骤添加:', data);
        if (!this.currentSessionId) return;

        const session = this.sessions.get(this.currentSessionId);
        let assistantMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);

        if (assistantMessage) {
            // 实时添加步骤到规划中
            if (!assistantMessage.metadata.planningSteps) {
                assistantMessage.metadata.planningSteps = [];
            }
            assistantMessage.metadata.planningSteps.push(data.step);

            // 更新进度
            assistantMessage.metadata.planningProgress = data.progress;
            assistantMessage.metadata.planningMessage = `已生成 ${data.step_number}/${data.total_steps} 个步骤`;

            this.updateAssistantMessage(assistantMessage);
        }
    }

    handleToolExecutionStart(data) {
        console.log('工具执行开始:', data);
        if (!this.currentSessionId) return;

        const session = this.sessions.get(this.currentSessionId);
        let assistantMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);

        if (assistantMessage) {
            // 更新当前执行状态
            assistantMessage.metadata.currentExecution = {
                step_id: data.step_id,
                tool: data.tool,
                action: data.action,
                status: 'executing',
                message: data.message
            };

            this.updateAssistantMessage(assistantMessage);
        }
    }

    handleToolExecutionProgress(data) {
        console.log('工具执行进度:', data);
        if (!this.currentSessionId) return;

        const session = this.sessions.get(this.currentSessionId);
        let assistantMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);

        if (assistantMessage && assistantMessage.metadata.currentExecution) {
            // 更新执行进度
            assistantMessage.metadata.currentExecution.progress = data.progress;
            assistantMessage.metadata.currentExecution.message = data.message;

            this.updateAssistantMessage(assistantMessage);
        }
    }

    handleToolExecutionError(data) {
        console.log('工具执行错误:', data);
        if (!this.currentSessionId) return;

        const session = this.sessions.get(this.currentSessionId);
        let assistantMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);

        if (assistantMessage) {
            // 添加错误信息
            if (!assistantMessage.metadata.executionErrors) {
                assistantMessage.metadata.executionErrors = [];
            }
            assistantMessage.metadata.executionErrors.push({
                step_id: data.step_id,
                error: data.error,
                timestamp: Date.now()
            });

            // 清除当前执行状态
            assistantMessage.metadata.currentExecution = null;

            this.updateAssistantMessage(assistantMessage);
        }
    }

    handleTaskError(data) {
        console.log('任务错误:', data);
        this.hideTypingIndicator();
        this.isProcessing = false;
        this.handleInputChange();

        if (!this.currentSessionId) return;

        const session = this.sessions.get(this.currentSessionId);
        let assistantMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);

        if (assistantMessage) {
            assistantMessage.content = `❌ 任务执行失败: ${data.error}`;
            assistantMessage.metadata.isActive = false;
            assistantMessage.metadata.hasError = true;
            this.updateAssistantMessage(assistantMessage);
        } else {
            this.addMessage('assistant', `❌ 任务执行失败: ${data.error}`);
        }

        this.currentAssistantMessageId = null;
        this.showNotification('任务执行失败', 'error');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    const chat = new AgentChat();
    chat.initSettings(); // 初始化设置功能
});
