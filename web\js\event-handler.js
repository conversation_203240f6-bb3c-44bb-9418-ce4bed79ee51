// 事件处理模块
class EventHandler {
    constructor(agentChat) {
        this.agentChat = agentChat;
        this.isProcessing = false;
    }

    // 初始化所有事件监听器
    initialize() {
        this.initializeBasicEvents();
        this.initializeUIEvents();
        this.initializeCollapsibleEvents();
    }

    // 初始化基本事件
    initializeBasicEvents() {
        const sendBtn = document.getElementById('send-btn');
        const chatInput = document.getElementById('chat-input');
        const newChatBtn = document.getElementById('new-chat-btn');
        const clearChatBtn = document.getElementById('clear-chat-btn');
        const modelSelect = document.getElementById('model-select');

        // 发送消息
        if (sendBtn) {
            sendBtn.addEventListener('click', () => this.handleSendMessage());
        }

        // 输入框事件
        if (chatInput) {
            chatInput.addEventListener('input', () => this.handleInputChange());
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.handleSendMessage();
                }
            });
        }

        // 新对话按钮
        if (newChatBtn) {
            newChatBtn.addEventListener('click', () => this.handleNewChat());
        }

        // 清空对话按钮
        if (clearChatBtn) {
            clearChatBtn.addEventListener('click', () => this.handleClearChat());
        }

        // 模型切换
        if (modelSelect) {
            modelSelect.addEventListener('change', (e) => this.handleModelChange(e.target.value));
        }
    }

    // 初始化UI事件
    initializeUIEvents() {
        // 示例任务按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('example-task')) {
                this.handleExampleTask(e);
            }
        });

        // 会话切换事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.session-item')) {
                this.handleSessionSwitch(e);
            }
        });
    }

    // 初始化可折叠区域事件
    initializeCollapsibleEvents() {
        document.addEventListener('click', (e) => {
            const collapsibleHeader = e.target.closest('.collapsible-header, .tool-collapsible-header');
            if (collapsibleHeader) {
                this.handleCollapsibleToggle(e, collapsibleHeader);
            }
        });
    }

    // 处理发送消息
    async handleSendMessage() {
        if (this.isProcessing) return;

        const chatInput = document.getElementById('chat-input');
        const message = chatInput.value.trim();

        if (!message) return;

        // 设置处理状态
        this.isProcessing = true;
        this.updateSendButtonState();

        try {
            await this.agentChat.sendMessage(message);
        } catch (error) {
            console.error('发送消息失败:', error);
            this.agentChat.showNotification('发送消息失败: ' + error.message, 'error');
        } finally {
            this.isProcessing = false;
            this.updateSendButtonState();
        }
    }

    // 处理输入变化
    handleInputChange() {
        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('send-btn');

        if (!chatInput || !sendBtn) return;

        // 重置高度以获取真实的scrollHeight
        chatInput.style.height = 'auto';

        const scrollHeight = chatInput.scrollHeight;
        const maxHeight = 120; // 减少最大高度，约3-4行
        const minHeight = 44;   // 单行高度
        const lineHeight = 20;  // 估算行高

        // 计算行数
        const lines = Math.floor(scrollHeight / lineHeight);

        // 只有当内容真正超过3行时才显示滚动条
        if (lines <= 3 || scrollHeight <= maxHeight) {
            // 内容较少，不需要滚动条
            chatInput.style.height = Math.max(scrollHeight, minHeight) + 'px';
            chatInput.style.overflowY = 'hidden';
        } else {
            // 内容超过3行，显示滚动条
            chatInput.style.height = maxHeight + 'px';
            chatInput.style.overflowY = 'auto';
        }

        // 更新发送按钮状态
        this.updateSendButtonState();
    }

    // 更新发送按钮状态
    updateSendButtonState() {
        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('send-btn');

        if (!chatInput || !sendBtn) return;

        const hasContent = chatInput.value.trim().length > 0;
        sendBtn.disabled = !hasContent || this.isProcessing;
    }

    // 处理新对话
    handleNewChat() {
        this.agentChat.createNewSession();
        this.agentChat.showNotification('已创建新对话', 'success');
    }

    // 处理清空对话
    handleClearChat() {
        if (confirm('确定要删除当前对话吗？此操作无法撤销。')) {
            this.agentChat.clearCurrentSession();
        }
    }

    // 处理模型切换
    handleModelChange(newModel) {
        this.agentChat.switchModel(newModel);
    }

    // 处理示例任务
    handleExampleTask(event) {
        const task = event.target.getAttribute('data-task');
        const chatInput = document.getElementById('chat-input');
        
        if (chatInput && task) {
            chatInput.value = task;
            chatInput.focus();
            this.handleInputChange();

            // 添加一个小动画效果
            event.target.style.transform = 'scale(0.95)';
            setTimeout(() => {
                event.target.style.transform = '';
            }, 150);
        }
    }

    // 处理会话切换
    handleSessionSwitch(event) {
        const sessionItem = event.target.closest('.session-item');
        if (sessionItem) {
            const sessionId = sessionItem.getAttribute('data-session-id');
            this.agentChat.switchToSession(sessionId);
        }
    }

    // 处理可折叠区域切换
    handleCollapsibleToggle(event, collapsibleHeader) {
        event.preventDefault();
        event.stopPropagation();

        const collapsibleSection = collapsibleHeader.parentElement;
        if (collapsibleSection && (
            collapsibleSection.classList.contains('collapsible-section') ||
            collapsibleSection.classList.contains('tool-collapsible-section')
        )) {
            collapsibleSection.classList.toggle('collapsed');
            
            // 更新图标
            const toggleIcon = collapsibleHeader.querySelector('.toggle-icon');
            if (toggleIcon) {
                if (collapsibleSection.classList.contains('collapsed')) {
                    toggleIcon.style.transform = 'rotate(-90deg)';
                } else {
                    toggleIcon.style.transform = 'rotate(0deg)';
                }
            }
        }
    }

    // 处理键盘快捷键
    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + Enter 发送消息
        if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
            event.preventDefault();
            this.handleSendMessage();
        }

        // Ctrl/Cmd + N 新对话
        if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
            event.preventDefault();
            this.handleNewChat();
        }

        // Escape 取消当前操作
        if (event.key === 'Escape') {
            if (this.isProcessing) {
                // 可以在这里添加取消当前请求的逻辑
                console.log('用户取消操作');
            }
        }
    }

    // 处理窗口大小变化
    handleWindowResize() {
        // 重新计算聊天区域高度
        this.adjustChatAreaHeight();
    }

    // 调整聊天区域高度
    adjustChatAreaHeight() {
        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages) {
            // 计算可用高度
            const windowHeight = window.innerHeight;
            const headerHeight = document.querySelector('.chat-header')?.offsetHeight || 0;
            const inputAreaHeight = document.querySelector('.chat-input-area')?.offsetHeight || 0;
            const availableHeight = windowHeight - headerHeight - inputAreaHeight - 40; // 40px for margins

            chatMessages.style.maxHeight = `${availableHeight}px`;
        }
    }

    // 设置处理状态
    setProcessingState(isProcessing) {
        this.isProcessing = isProcessing;
        this.updateSendButtonState();
        
        // 更新UI状态
        const chatInput = document.getElementById('chat-input');
        if (chatInput) {
            chatInput.disabled = isProcessing;
        }
    }

    // 显示加载状态
    showLoadingState() {
        this.setProcessingState(true);
        // 可以在这里添加更多的加载状态UI
    }

    // 隐藏加载状态
    hideLoadingState() {
        this.setProcessingState(false);
        // 可以在这里添加更多的加载状态UI清理
    }

    // 绑定全局事件
    bindGlobalEvents() {
        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
        
        // 窗口大小变化
        window.addEventListener('resize', () => this.handleWindowResize());
        
        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // 页面重新可见时，可以检查连接状态等
                this.agentChat.checkConnectionStatus();
            }
        });
    }

    // 解绑事件（清理）
    cleanup() {
        // 移除全局事件监听器
        document.removeEventListener('keydown', this.handleKeyboardShortcuts);
        window.removeEventListener('resize', this.handleWindowResize);
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    }
}

// 导出事件处理器
window.EventHandler = EventHandler;
