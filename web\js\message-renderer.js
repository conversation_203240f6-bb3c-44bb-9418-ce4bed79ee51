// 消息渲染模块
class MessageRenderer {
    constructor(configManager) {
        this.configManager = configManager;
        this.typewriterIntervals = new Map();
    }

    // 渲染单个消息
    renderMessage(message) {
        const time = this.formatTime(message.timestamp);

        // 根据消息类型选择不同的渲染方式
        switch (message.role) {
            case 'user':
                return this.renderUserMessage(message, time);
            case 'assistant':
                return this.renderAssistantMessage(message, time);
            case 'tool-execution':
                return this.renderToolExecutionMessage(message, time);
            case 'thinking':
                return this.renderThinkingMessage(message, time);
            case 'system':
                return this.renderSystemMessage(message, time);
            default:
                return this.renderAssistantMessage(message, time);
        }
    }

    // 渲染用户消息
    renderUserMessage(message, time) {
        return `
            <div class="message user" data-message-id="${message.id}">
                <div class="message-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="message-content">
                    <div class="message-bubble">
                        ${this.formatMessageContent(message.content)}
                    </div>
                    <div class="message-time">${time}</div>
                </div>
            </div>
        `;
    }

    // 渲染助手消息
    renderAssistantMessage(message, time) {
        // 优先使用消息中的模型名称，然后使用配置管理器中的当前模型
        const modelName = message.metadata?.model_name ||
                         message.metadata?.model ||
                         this.configManager?.getModelDisplayName(this.configManager?.currentModel) ||
                         '助手';
        const isActive = message.metadata?.isActive || false;
        const activeClass = isActive ? 'active' : '';

        // 构建消息内容
        let contentHtml = '';

        // 规划步骤区域
        if (message.metadata?.planningSteps && message.metadata.planningSteps.length > 0) {
            contentHtml += this.renderPlanningSection(message.metadata.planningSteps);
        }

        // 思维过程区域
        if (message.metadata?.thinkingProcess && message.metadata.thinkingProcess.length > 0) {
            contentHtml += this.renderThinkingSection(message.metadata.thinkingProcess);
        }

        // LLM思考过程区域（来自think标签）
        if (message.metadata?.llmThoughtProcess && message.metadata.llmThoughtProcess.length > 0) {
            contentHtml += this.renderLlmThinkingSection(message.metadata.llmThoughtProcess);
        }

        // 工具执行区域
        if (message.metadata?.toolExecutions && message.metadata.toolExecutions.length > 0) {
            contentHtml += this.renderToolExecutionsSection(message.metadata.toolExecutions);
        }

        // 主要回复区域
        if (message.content) {
            contentHtml += `
                <div class="main-response" data-role="main-response">
                    ${this.formatMessageContent(message.content)}
                </div>
            `;
        }

        // 参考文献区域
        if (message.metadata?.references && message.metadata.references.length > 0) {
            contentHtml += this.renderReferencesSection(message.metadata.references);
        }

        return `
            <div class="message assistant ${activeClass}" data-message-id="${message.id}">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <span class="model-name">${modelName}</span>
                        ${isActive ? '<span class="typing-indicator">正在思考...</span>' : ''}
                    </div>
                    <div class="message-bubble">
                        ${contentHtml}
                    </div>
                    <div class="message-time">${time}</div>
                </div>
            </div>
        `;
    }

    // 渲染规划步骤区域
    renderPlanningSection(planningSteps) {
        const stepsHtml = planningSteps.map((step, index) => {
            const statusClass = step.status || 'pending';
            const statusIcon = this.getStatusIcon(statusClass);

            return `
                <div class="planning-step ${statusClass}" data-step-id="${step.id}">
                    <div class="step-header">
                        <span class="step-number">${index + 1}</span>
                        <span class="step-description">${step.description}</span>
                        <span class="step-status">${statusIcon}</span>
                    </div>
                    ${step.result ? `<div class="step-result">${step.result}</div>` : ''}
                </div>
            `;
        }).join('');

        return `
            <div class="collapsible-section planning-section" data-role="planning-section">
                <div class="collapsible-header">
                    <span>📋 任务规划</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="collapsible-content">
                    <div class="planning-steps">
                        ${stepsHtml}
                    </div>
                </div>
            </div>
        `;
    }

    // 渲染思维过程区域
    renderThinkingSection(thinkingProcess) {
        const thoughtsHtml = thinkingProcess.map((thought, index) => `
            <div class="thinking-step">
                <div class="thinking-content">${this.formatMessageContent(thought.content)}</div>
            </div>
        `).join('');

        return `
            <div class="collapsible-section thinking-section collapsed" data-role="thinking-section">
                <div class="collapsible-header">
                    <span>🧠 思维过程</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="collapsible-content">
                    <div class="thinking-steps">
                        ${thoughtsHtml}
                    </div>
                </div>
            </div>
        `;
    }

    // 渲染LLM思考过程区域
    renderLlmThinkingSection(llmThoughts) {
        const thoughtsHtml = llmThoughts.map((thought, index) => `
            <div class="llm-thinking-step">
                <div class="thinking-content">${this.formatMessageContent(thought.content)}</div>
            </div>
        `).join('');

        return `
            <div class="collapsible-section llm-thinking-section collapsed" data-role="llm-thinking-section">
                <div class="collapsible-header">
                    <span>🤔 模型思考</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="collapsible-content">
                    <div class="llm-thinking-steps">
                        ${thoughtsHtml}
                    </div>
                </div>
            </div>
        `;
    }

    // 渲染工具执行区域
    renderToolExecutionsSection(toolExecutions) {
        const toolsHtml = toolExecutions.map(tool => this.renderToolExecution(tool)).join('');

        return `
            <div class="collapsible-section tools-section" data-role="tools-section">
                <div class="collapsible-header">
                    <span>🔧 工具执行</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="collapsible-content">
                    <div class="tool-executions">
                        ${toolsHtml}
                    </div>
                </div>
            </div>
        `;
    }

    // 渲染单个工具执行
    renderToolExecution(tool) {
        const time = new Date(tool.timestamp).toLocaleTimeString();
        const toolDisplayName = this.getToolDisplayName(tool.tool || tool.tool_name);
        const toolStatusText = this.getStatusText(tool.status || 'completed');
        const toolStatusClass = (tool.status || 'completed').toLowerCase();

        const toolInputHtml = tool.parameters ? `
            <div class="tool-action">
                <strong>🎯 操作:</strong> ${tool.action || 'N/A'}
            </div>
            <div class="tool-params">
                <strong>📝 参数:</strong>
                <pre><code>${this.escapeHtml(JSON.stringify(tool.parameters, null, 2))}</code></pre>
            </div>` : '';

        const resultBlockHtml = tool.result ? `
            <div class="tool-result">
                <strong>📊 执行结果:</strong>
                <div class="result-content">${this.formatMessageContent(tool.result)}</div>
            </div>
        ` : `<div class="tool-result"><strong>📊 执行结果:</strong><div class="result-content">无结果</div></div>`;

        const executionTimeHtml = `
            <div class="tool-time" style="margin-top: var(--spacing-sm);">
                ⏱️ 执行时间: ${time}
                ${tool.execution_time ? ` | 耗时: ${tool.execution_time}` : ''}
            </div>`;

        return `
            <div class="tool-execution" data-tool-id="${tool.id || tool.tool_id || ''}">
                <div class="tool-collapsible-section tool-execution-details">
                    <div class="tool-collapsible-header tool-execution-main-header">
                        <div class="tool-header-content">
                            <span class="tool-name">
                                <i class="fas fa-wrench"></i> ${toolDisplayName}
                            </span>
                            <span class="tool-status ${toolStatusClass}">${toolStatusText}</span>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </div>
                    <div class="tool-collapsible-content">
                        ${toolInputHtml}
                        ${!tool.result ? executionTimeHtml : ''}
                    </div>
                </div>
                ${tool.result || !tool.parameters ? `
                <div class="tool-collapsible-section tool-execution-result collapsed">
                    <div class="tool-collapsible-header tool-result-header">
                        <span>执行结果</span>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </div>
                    <div class="tool-collapsible-content">
                        ${resultBlockHtml}
                        ${tool.result ? executionTimeHtml : ''}
                    </div>
                </div>` : ''}
            </div>
        `;
    }

    // 渲染参考文献区域
    renderReferencesSection(references) {
        const referencesHtml = references.map(ref => `
            <div class="reference-item">
                <span class="reference-number">[${ref.index}]</span>
                <a href="${ref.url}" target="_blank" class="reference-link">
                    ${ref.title}
                </a>
            </div>
        `).join('');

        return `
            <div class="collapsible-section references-section collapsed" data-role="references-section">
                <div class="collapsible-header">
                    <span>📚 参考文献</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="collapsible-content">
                    <div class="references-list">
                        ${referencesHtml}
                    </div>
                </div>
            </div>
        `;
    }

    // 渲染系统消息
    renderSystemMessage(message, time) {
        return `
            <div class="message system" data-message-id="${message.id}">
                <div class="message-content">
                    <div class="system-message">
                        <i class="fas fa-info-circle"></i>
                        ${this.formatMessageContent(message.content)}
                    </div>
                    <div class="message-time">${time}</div>
                </div>
            </div>
        `;
    }

    // 格式化消息内容
    formatMessageContent(content) {
        if (!content) return '';

        // 转换Markdown格式
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    // 转义HTML
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 格式化时间
    formatTime(date) {
        return new Date(date).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 获取状态图标
    getStatusIcon(status) {
        const icons = {
            'pending': '⏳',
            'in_progress': '🔄',
            'completed': '✅',
            'failed': '❌',
            'skipped': '⏭️'
        };
        return icons[status] || '❓';
    }

    // 获取状态文本
    getStatusText(status) {
        const texts = {
            'pending': '等待中',
            'in_progress': '执行中',
            'completed': '已完成',
            'failed': '失败',
            'skipped': '已跳过'
        };
        return texts[status] || status;
    }

    // 获取工具显示名称
    getToolDisplayName(toolName) {
        const displayNames = {
            'WebSearchTool': '网络搜索',
            'CalculatorTool': '数学计算',
            'FileOperationsTool': '文件操作',
            'GeneralTool': '通用分析'
        };
        return displayNames[toolName] || toolName;
    }

    // 打字机效果
    typewriterEffect(element, text, speed = 20) {
        if (!element || !text) return Promise.resolve();

        return new Promise((resolve) => {
            element.innerHTML = '';
            let index = 0;

            const interval = setInterval(() => {
                if (index < text.length) {
                    element.innerHTML += text.charAt(index);
                    index++;
                } else {
                    clearInterval(interval);
                    resolve();
                }
            }, speed);
        });
    }

    // 清理打字机效果
    clearTypewriterEffect(messageId) {
        if (this.typewriterIntervals.has(messageId)) {
            clearInterval(this.typewriterIntervals.get(messageId));
            this.typewriterIntervals.delete(messageId);
        }
    }

    // 清理所有打字机效果
    clearAllTypewriterEffects() {
        this.typewriterIntervals.forEach(interval => clearInterval(interval));
        this.typewriterIntervals.clear();
    }
}

// 导出消息渲染器
window.MessageRenderer = MessageRenderer;
