"""
任务级别的日志记录器

这个模块提供按任务ID组织的日志记录功能，解决了原有日志分散、查找困难的问题。

设计目标:
1. 一个任务的所有日志都在同一个目录下
2. 支持不同类型的日志分类（LLM、执行、API等）
3. 提供统一的完整日志文件
4. 便于查找和调试特定任务
"""

import logging
import json
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from .env_config import get_config as get_env_config


class TaskLogger:
    """任务级别的日志记录器

    为每个任务创建独立的日志目录，包含：
    - full.log: 完整的任务日志（包含所有类型）
    - llm.log: LLM输入输出日志
    - execution.log: 任务执行流程日志
    - api.log: API调用日志
    - metadata.json: 任务元数据
    """

    def __init__(self, task_id: str, task_description: str = ""):
        self.task_id = task_id
        self.task_description = task_description
        self.start_time = datetime.now()

        # 创建任务日志目录 - 使用环境配置的日志根目录
        env_config = get_env_config()
        log_root = env_config.log.root_dir
        self.task_dir = Path(f"{log_root}/tasks/{task_id}")
        self.task_dir.mkdir(parents=True, exist_ok=True)

        # 创建不同类型的日志记录器
        self.full_logger = self._create_logger("full", "full.log")
        self.llm_logger = self._create_logger("llm", "llm.log")
        self.execution_logger = self._create_logger("execution", "execution.log")
        self.api_logger = self._create_logger("api", "api.log")

        # 保存任务元数据
        self._save_metadata()

        # 记录任务开始
        self.log_full(f"🚀 任务开始 - ID: {task_id}")
        if task_description:
            self.log_full(f"📝 任务描述: {task_description}")
        self.log_full(f"⏰ 开始时间: {self.start_time.isoformat()}")

    def _create_logger(self, log_type: str, filename: str) -> logging.Logger:
        """创建特定类型的日志记录器"""
        logger_name = f"{self.task_id}_{log_type}"
        log_file = self.task_dir / filename

        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.INFO)

        # 清除现有处理器避免重复
        logger.handlers.clear()
        logger.propagate = False

        # 创建文件处理器
        file_handler = logging.FileHandler(log_file, encoding="utf-8")
        file_handler.setLevel(logging.INFO)

        # 创建格式器
        formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
        file_handler.setFormatter(formatter)

        # 添加处理器
        logger.addHandler(file_handler)

        return logger

    def _save_metadata(self):
        """保存任务元数据"""
        metadata = {
            "task_id": self.task_id,
            "task_description": self.task_description,
            "start_time": self.start_time.isoformat(),
            "log_files": {
                "full": "full.log",
                "llm": "llm.log",
                "execution": "execution.log",
                "api": "api.log",
            },
            "status": "running",
        }

        metadata_file = self.task_dir / "metadata.json"
        with open(metadata_file, "w", encoding="utf-8") as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

    def log_full(self, message: str, level: str = "INFO"):
        """记录到完整日志"""
        getattr(self.full_logger, level.lower())(message)

    def log_llm(self, message: str, level: str = "INFO"):
        """记录LLM相关日志"""
        getattr(self.llm_logger, level.lower())(message)
        self.log_full(f"[LLM] {message}", level)

    def log_execution(self, message: str, level: str = "INFO"):
        """记录执行相关日志"""
        getattr(self.execution_logger, level.lower())(message)
        self.log_full(f"[EXEC] {message}", level)

    def log_api(self, message: str, level: str = "INFO"):
        """记录API相关日志"""
        getattr(self.api_logger, level.lower())(message)
        self.log_full(f"[API] {message}", level)

    def log_llm_input(self, model: str, messages: list, **kwargs):
        """记录LLM输入"""
        self.log_llm(f"🤖 LLM调用开始 - 模型: {model}")
        self.log_llm(f"📝 输入消息数量: {len(messages)}")

        for i, msg in enumerate(messages, 1):
            role = (
                msg.get("role", "unknown")
                if isinstance(msg, dict)
                else getattr(msg, "role", "unknown")
            )
            content = (
                msg.get("content", "")
                if isinstance(msg, dict)
                else getattr(msg, "content", "")
            )

            self.log_llm(f"📋 消息 {i} - 角色: {role}")
            content_preview = content[:200] + "..." if len(content) > 200 else content
            self.log_llm(f"💬 内容预览: {content_preview}")
            if len(content) > 200:
                self.log_llm(f"📊 完整内容长度: {len(content)} 字符")

        # 记录其他参数
        for key, value in kwargs.items():
            self.log_llm(f"⚙️ {key}: {value}")

    def log_llm_output(self, response: str, **kwargs):
        """记录LLM输出"""
        self.log_llm(f"✅ LLM响应完成 - 总长度: {len(response)} 字符")
        response_preview = response[:300] + "..." if len(response) > 300 else response
        self.log_llm(f"📤 响应预览: {response_preview}")

        if len(response) > 300:
            self.log_llm(f"📋 完整响应内容: {response}")

        # 分析响应内容类型
        if "<think>" in response and "</think>" in response:
            self.log_llm("🧠 检测到COT思考标签")
        if "[" in response and "]" in response:
            self.log_llm("📋 检测到JSON格式内容")
        if "```" in response:
            self.log_llm("💻 检测到代码块内容")

        # 记录其他信息
        for key, value in kwargs.items():
            self.log_llm(f"📊 {key}: {value}")

    def log_tool_execution(
        self,
        tool_name: str,
        action: str,
        parameters: dict,
        result: Any = None,
        error: str = None,
    ):
        """记录工具执行"""
        self.log_execution(f"🔧 工具执行 - {tool_name}.{action}")
        self.log_execution(f"📋 参数: {parameters}")

        if result is not None:
            result_str = str(result)
            self.log_execution(f"✅ 执行成功 - 结果长度: {len(result_str)} 字符")
            if len(result_str) > 200:
                self.log_execution(f"📋 结果预览: {result_str[:200]}...")
            else:
                self.log_execution(f"📋 执行结果: {result_str}")

        if error:
            self.log_execution(f"❌ 执行失败: {error}")

    def finish_task(self, success: bool = True, summary: str = ""):
        """标记任务完成"""
        end_time = datetime.now()
        duration = end_time - self.start_time

        status = "✅ 成功" if success else "❌ 失败"
        self.log_full(f"🏁 任务完成 - 状态: {status}")
        self.log_full(f"⏰ 结束时间: {end_time.isoformat()}")
        self.log_full(f"⏱️ 执行时长: {duration.total_seconds():.2f} 秒")

        if summary:
            self.log_full(f"📝 任务总结: {summary}")

        # 更新元数据
        metadata_file = self.task_dir / "metadata.json"
        if metadata_file.exists():
            with open(metadata_file, "r", encoding="utf-8") as f:
                metadata = json.load(f)

            metadata.update(
                {
                    "end_time": end_time.isoformat(),
                    "duration_seconds": duration.total_seconds(),
                    "status": "completed" if success else "failed",
                    "summary": summary,
                }
            )

            with open(metadata_file, "w", encoding="utf-8") as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)


class TaskLoggerManager:
    """任务日志记录器管理器"""

    _task_loggers: Dict[str, TaskLogger] = {}

    @classmethod
    def get_task_logger(cls, task_id: str, task_description: str = "") -> TaskLogger:
        """获取或创建任务日志记录器"""
        if task_id not in cls._task_loggers:
            cls._task_loggers[task_id] = TaskLogger(task_id, task_description)
        return cls._task_loggers[task_id]

    @classmethod
    def finish_task(cls, task_id: str, success: bool = True, summary: str = ""):
        """完成任务并清理日志记录器"""
        if task_id in cls._task_loggers:
            cls._task_loggers[task_id].finish_task(success, summary)
            del cls._task_loggers[task_id]

    @classmethod
    def list_task_logs(cls) -> list:
        """列出所有任务日志目录"""
        env_config = get_env_config()
        logs_dir = Path(f"{env_config.log.root_dir}/tasks")
        if not logs_dir.exists():
            return []

        task_logs = []
        for task_dir in logs_dir.iterdir():
            if task_dir.is_dir():
                metadata_file = task_dir / "metadata.json"
                if metadata_file.exists():
                    try:
                        with open(metadata_file, "r", encoding="utf-8") as f:
                            metadata = json.load(f)
                        task_logs.append(metadata)
                    except Exception:
                        pass

        # 按开始时间排序
        task_logs.sort(key=lambda x: x.get("start_time", ""), reverse=True)
        return task_logs

    @classmethod
    def get_task_log_path(cls, task_id: str) -> Optional[Path]:
        """获取任务日志目录路径"""
        env_config = get_env_config()
        task_dir = Path(f"{env_config.log.root_dir}/tasks/{task_id}")
        return task_dir if task_dir.exists() else None


def get_task_logger(task_id: str, task_description: str = "") -> TaskLogger:
    """便捷函数：获取任务日志记录器"""
    return TaskLoggerManager.get_task_logger(task_id, task_description)
