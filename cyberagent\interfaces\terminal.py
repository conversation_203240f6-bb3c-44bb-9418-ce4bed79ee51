"""
Terminal interface for AutoAgent

This module provides a direct terminal interface for AutoAgent,
maintained for backward compatibility and direct terminal usage.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from autoagent.agent import AutoAgent


async def main():
    """Main function for terminal interface"""
    agent = AutoAgent()

    try:
        await agent.start_interactive_mode()
    finally:
        await agent.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
