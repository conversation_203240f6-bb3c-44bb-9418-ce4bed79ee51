#!/usr/bin/env python3
"""
CyberAgent 快速启动器

这个脚本提供了一个快速启动 CyberAgent Web 界面的方式，
用于测试和演示目的。
"""

import sys
import time
import webbrowser
import subprocess
from pathlib import Path
from threading import Timer

# 将当前目录添加到Python路径
sys.path.insert(0, str(Path(__file__).parent))


def open_browser(url: str):
    """延迟后打开浏览器"""
    print("🌐 正在打开浏览器...")
    webbrowser.open(url)


def main():
    """主启动函数"""
    try:
        # 导入环境配置
        from cyberagent.core.env_config import get_config

        # 获取配置
        config = get_config()

        # 构建访问地址
        server_url = f"http://{config.server.host}:{config.server.port}"

        print("🤖 CyberAgent 快速启动器")
        if config.server.auto_open_browser:
            print("⏱️  浏览器将在 3 秒后自动打开")

        # 显示配置摘要
        config.print_config_summary()
        print()

        # Schedule browser opening if enabled
        timer = None
        if config.server.auto_open_browser:
            timer = Timer(3.0, lambda: open_browser(server_url))
            timer.start()

        # 导入并启动Web界面
        from cyberagent.interfaces.api_server import AgentAPI

        # 创建API实例
        api = AgentAPI()

        # 启动服务器
        api.run(host=config.server.host, port=config.server.port)

    except KeyboardInterrupt:
        print("\n")
        print("👋 CyberAgent 演示已被用户停止")
        print("感谢您测试 CyberAgent！")
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保您在正确的目录中，并且已安装所有依赖")
        print("   尝试运行: uv sync")
    except Exception as e:
        print(f"💥 启动演示时出错: {e}")
        print("💡 请检查 logs 目录获取更多详细信息")
    finally:
        if "timer" in locals():
            timer.cancel()


if __name__ == "__main__":
    main()
