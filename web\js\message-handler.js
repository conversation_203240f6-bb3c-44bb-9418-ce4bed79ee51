// WebSocket消息处理模块
class MessageHandler {
    constructor(agentChat) {
        this.agentChat = agentChat;
        this.currentAssistantMessageId = null;
    }

    // 处理WebSocket消息
    handleMessage(message) {
        // 检查消息是否属于当前会话
        if (message.data && message.data.session_id &&
            message.data.session_id !== this.agentChat.sessionManager.currentSessionId) {
            return;
        }

        // 根据消息类型分发处理
        switch (message.type) {
            case 'model_info':
                this.handleModelInfo(message.data);
                break;
            case 'planning_thinking':
                this.handlePlanningThinking(message.data);
                break;
            case 'planning_step_added':
                this.handlePlanningStepAdded(message.data);
                break;
            case 'planning_complete':
                this.handlePlanningComplete(message.data);
                break;
            case 'execution_start':
                this.handleExecutionStart(message.data);
                break;
            case 'step_start':
                this.handleStepStart(message.data);
                break;
            case 'cot_step':
                this.handleCOTStep(message.data);
                break;
            case 'tool_execution_start':
                this.handleToolExecutionStart(message.data);
                break;
            case 'tool_execution_progress':
                this.handleToolExecutionProgress(message.data);
                break;
            case 'tool_call':
                this.handleToolCall(message.data);
                break;
            case 'step_complete':
                this.handleStepComplete(message.data);
                break;
            case 'execution_complete':
                this.handleExecutionComplete(message.data);
                break;
            case 'tool_execution_error':
                this.handleToolExecutionError(message.data);
                break;
            default:
                if (this.agentChat.configManager.debugMode) {
                    console.log('未知消息类型:', message.type);
                }
        }
    }

    // 处理模型信息
    handleModelInfo(data) {
        if (data.model_name && data.model_name !== this.agentChat.configManager.currentModel) {
            this.agentChat.configManager.currentModel = data.model_name;
            this.agentChat.updateModelStatusDisplay();
        }
    }

    // 处理规划思维
    handlePlanningThinking(data) {
        // 可以在这里更新UI，显示规划思维过程
    }

    // 处理规划步骤添加
    handlePlanningStepAdded(data) {
        // 可以在这里更新UI，显示新添加的规划步骤
    }

    // 处理规划完成
    handlePlanningComplete(data) {
        // 如果包含模型信息，更新当前模型
        if (data.model_name && data.model_name !== this.agentChat.configManager.currentModel) {
            this.agentChat.configManager.currentModel = data.model_name;
            this.agentChat.updateModelStatusDisplay();
        }

        // 规划功能已废除，不再处理规划步骤
        console.log('规划功能已废除，跳过规划步骤处理');
    }

    // 处理执行开始
    handleExecutionStart(data) {
        // 可以在这里更新UI，例如显示执行状态
    }

    // 处理步骤开始
    handleStepStart(data) {
        if (!this.agentChat.sessionManager.currentSessionId) return;

        const session = this.agentChat.sessionManager.getCurrentSession();
        let assistantMessage = session.messages.find(m => m.role === 'assistant' && m.metadata.isActive);

        if (assistantMessage) {
            // 更新 metadata.steps
            if (!assistantMessage.metadata.steps) {
                assistantMessage.metadata.steps = {};
            }
            assistantMessage.metadata.steps[data.step_id] = {
                status: data.status,
                progress: data.progress
            };

            // 更新 planningSteps 中对应步骤的状态
            if (assistantMessage.metadata.planningSteps) {
                const stepToUpdate = assistantMessage.metadata.planningSteps.find(step => step.id === data.step_id);
                if (stepToUpdate) {
                    stepToUpdate.status = data.status;
                    // 更新UI显示
                    this.agentChat.updateAssistantMessage(assistantMessage);
                }
            }
        }
    }

    // 处理COT步骤
    handleCOTStep(data) {
        // 处理思维链步骤
        if (!this.agentChat.sessionManager.currentSessionId) return;

        const session = this.agentChat.sessionManager.getCurrentSession();
        let assistantMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);

        if (assistantMessage && data.content) {
            if (!assistantMessage.metadata.thinkingProcess) {
                assistantMessage.metadata.thinkingProcess = [];
            }
            assistantMessage.metadata.thinkingProcess.push({
                content: data.content,
                timestamp: new Date()
            });

            // 更新UI显示
            this.agentChat.updateAssistantMessage(assistantMessage);
        }
    }

    // 处理工具执行开始
    handleToolExecutionStart(data) {
        // 可以在这里更新UI，显示工具执行开始状态
    }

    // 处理工具执行进度
    handleToolExecutionProgress(data) {
        // 可以在这里更新UI，显示工具执行进度
    }

    // 处理工具调用
    handleToolCall(data) {
        if (!this.agentChat.sessionManager.currentSessionId) return;

        const session = this.agentChat.sessionManager.getCurrentSession();
        let assistantMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);

        if (!assistantMessage) {
            assistantMessage = this.agentChat.sessionManager.addMessage('assistant', '', {
                isActive: true,
                toolExecutions: [],
                thinkingProcess: [],
                references: []
            });
            this.currentAssistantMessageId = assistantMessage.id;
        } else {
            // 确保元数据存在
            if (!assistantMessage.metadata) {
                assistantMessage.metadata = {};
            }
            if (!assistantMessage.metadata.toolExecutions) {
                assistantMessage.metadata.toolExecutions = [];
            }
            if (!assistantMessage.metadata.references) {
                assistantMessage.metadata.references = [];
            }
        }

        const toolExecution = {
            tool: data.tool,
            action: data.action,
            parameters: data.parameters,
            result: data.result,
            status: data.status || 'completed',
            timestamp: data.timestamp || new Date().toISOString()
        };

        assistantMessage.metadata.toolExecutions.push(toolExecution);

        // 如果是 web_search 工具，提取参考文献
        if (data.tool === 'web_search' && data.result && typeof data.result === 'string') {
            this.extractReferences(data.result, assistantMessage);
        }

        this.agentChat.updateAssistantMessage(assistantMessage);
    }

    // 提取参考文献
    extractReferences(result, assistantMessage) {
        const references = [];

        // 正则表达式匹配搜索结果条目
        const regex = /(\d+)\.\s*(.*?)\n\s*来源:.*?\n\s*摘要:.*?\n\s*链接:\s*(https?:\/\/[^\s]+)/g;
        let match;
        while ((match = regex.exec(result)) !== null) {
            references.push({
                index: parseInt(match[1]),
                title: match[2].trim(),
                url: match[3].trim()
            });
        }

        // 也尝试从详细网页内容部分提取
        const pageContentRegex = /\*\*网页\s*(\d+):\s*(.*?)\*\*\n链接:\s*(https?:\/\/[^\s]+)/g;
        while ((match = pageContentRegex.exec(result)) !== null) {
            const existingRef = references.find(ref => ref.url === match[3].trim());
            if (!existingRef) {
                references.push({
                    index: parseInt(match[1]),
                    title: match[2].trim(),
                    url: match[3].trim()
                });
            }
        }

        // 对参考文献按 index 排序并去重
        const uniqueReferences = Array.from(new Map(references.map(ref => [ref.url, ref])).values())
                                   .sort((a, b) => a.index - b.index);

        // 重新编号，确保序号从1开始连续
        assistantMessage.metadata.references = uniqueReferences.map((ref, idx) => ({
            ...ref,
            originalIndex: ref.index,
            index: idx + 1
        }));
    }

    // 处理步骤完成
    handleStepComplete(data) {
        if (!this.agentChat.sessionManager.currentSessionId) return;

        const session = this.agentChat.sessionManager.getCurrentSession();
        let assistantMessage = session.messages.find(m => m.role === 'assistant' && m.metadata.isActive);

        if (assistantMessage) {
            // 更新 metadata.steps
            if (!assistantMessage.metadata.steps) {
                assistantMessage.metadata.steps = {};
            }
            assistantMessage.metadata.steps[data.step_id] = {
                status: data.status,
                result: data.result,
                progress: data.progress
            };

            // 更新 planningSteps 中对应步骤的状态
            if (assistantMessage.metadata.planningSteps) {
                const stepToUpdate = assistantMessage.metadata.planningSteps.find(step => step.id === data.step_id);
                if (stepToUpdate) {
                    stepToUpdate.status = data.status;
                    if (data.result) {
                        stepToUpdate.result = data.result;
                    }
                    // 更新UI显示
                    this.agentChat.updateAssistantMessage(assistantMessage);
                }
            }
        }
    }

    // 处理执行完成
    handleExecutionComplete(data) {
        // 隐藏打字指示器
        this.agentChat.hideTypingIndicator();
        this.agentChat.eventHandler.setProcessingState(false);

        // 如果包含模型信息，更新当前模型
        if (data.model_name && data.model_name !== this.agentChat.configManager.currentModel) {
            this.agentChat.configManager.currentModel = data.model_name;
            this.agentChat.updateModelStatusDisplay();
        }

        if (!this.agentChat.sessionManager.currentSessionId) return;

        const session = this.agentChat.sessionManager.getCurrentSession();
        let assistantMessage = session.messages.find(m => m.id === this.currentAssistantMessageId);

        if (assistantMessage) {
            // 标记为完成
            assistantMessage.metadata.isActive = false;

            // 保存模型信息到消息元数据
            if (data.model_name) {
                assistantMessage.metadata.model_name = data.model_name;
            }

            // 添加简洁的任务状态总结
            if (data.summary) {
                assistantMessage.metadata.taskSummary = data.summary;
            }

            // 使用智能回复（如果有）或生成用户友好的回答
            let finalAnswer;
            if (data.intelligent_response) {
                finalAnswer = data.intelligent_response;
            } else {
                finalAnswer = this.agentChat.generateUserFriendlyAnswer(assistantMessage);
            }

            // 处理think标签（仅对COT模型）
            const processedAnswer = this.agentChat.configManager.processThinkContent(finalAnswer);

            if (typeof processedAnswer === 'object' && processedAnswer.thinkingSteps && processedAnswer.thinkingSteps.length > 0) {
                // LLM response included <think> tags
                assistantMessage.content = processedAnswer.content;

                // Store LLM's <think> steps
                if (!assistantMessage.metadata.llmThoughtProcess) {
                    assistantMessage.metadata.llmThoughtProcess = [];
                }
                assistantMessage.metadata.llmThoughtProcess = processedAnswer.thinkingSteps;
            } else {
                // No <think> tags in LLM response
                assistantMessage.content = processedAnswer;
            }

            assistantMessage.metadata.finalSummarySet = true;

            // 使用流畅更新显示最终回复
            this.agentChat.updateAssistantMessage(assistantMessage);

            this.currentAssistantMessageId = null; // 清空当前活跃助手消息ID
        } else {
            console.error("Error: currentAssistantMessageId not found for execution_complete.");
            this.agentChat.sessionManager.addMessage('assistant', data.intelligent_response || data.summary || "执行完成，但无法更新原始消息。");
            this.currentAssistantMessageId = null;
        }
    }

    // 处理工具执行错误
    handleToolExecutionError(data) {
        this.agentChat.hideTypingIndicator();
        this.agentChat.eventHandler.setProcessingState(false);

        this.agentChat.sessionManager.addMessage('assistant', `执行过程中出现错误：${data.error}`);
    }

    // 设置当前助手消息ID
    setCurrentAssistantMessageId(messageId) {
        this.currentAssistantMessageId = messageId;
    }

    // 获取当前助手消息ID
    getCurrentAssistantMessageId() {
        return this.currentAssistantMessageId;
    }

    // 清理当前助手消息ID
    clearCurrentAssistantMessageId() {
        this.currentAssistantMessageId = null;
    }
}

// 导出消息处理器
window.MessageHandler = MessageHandler;
