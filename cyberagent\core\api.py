"""
AutoGen-Compatible API Interface for AutoAgent

This module provides standardized API interfaces that are compatible with
Microsoft AutoGen framework, enabling seamless integration and migration.
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import BaseModel
from enum import Enum


class MessageRole(str, Enum):
    """Message roles compatible with AutoGen"""

    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    FUNCTION = "function"


class TaskStatus(str, Enum):
    """Task execution status"""

    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AgentMessage(BaseModel):
    """AutoGen-compatible message format"""

    role: MessageRole
    content: str
    name: Optional[str] = None
    function_call: Optional[Dict[str, Any]] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    metadata: Dict[str, Any] = {}


class ToolInfo(BaseModel):
    """Tool information"""

    name: str
    description: str
    category: str
    actions: List[str] = []
    parameters: Dict[str, Any] = {}


class TaskStep(BaseModel):
    """Individual task step"""

    id: Union[str, int]
    tool: str
    action: str
    parameters: Dict[str, Any]
    description: str
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[Any] = None
    error: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class ConversationRequest(BaseModel):
    """AutoGen-compatible conversation request"""

    messages: List[AgentMessage]
    session_id: Optional[str] = None
    model: Optional[str] = None
    max_turns: int = 10
    tools: Optional[List[str]] = None
    context: Dict[str, Any] = {}


class ConversationResponse(BaseModel):
    """AutoGen-compatible conversation response"""

    success: bool
    session_id: str
    messages: List[AgentMessage]
    summary: Optional[str] = None
    metadata: Dict[str, Any] = {}
    error: Optional[str] = None


class AgentConfig(BaseModel):
    """AutoGen-compatible agent configuration"""

    name: str
    role: str = "assistant"
    system_message: Optional[str] = None
    model: Optional[str] = None
    tools: List[str] = []
    max_consecutive_auto_reply: int = 10
    human_input_mode: str = "NEVER"  # NEVER, TERMINATE, ALWAYS


class TaskRequest(BaseModel):
    """Legacy task execution request for backward compatibility"""

    task: str
    session_id: Optional[str] = None
    model: Optional[str] = None
    context: Dict[str, Any] = {}


class TaskResponse(BaseModel):
    """Task execution response"""

    success: bool
    task_id: str
    message: str
    summary: Optional[str] = None
    steps: List[TaskStep] = []
    related_tasks: List[str] = []
    metadata: Dict[str, Any] = {}
    error: Optional[str] = None


class SystemStatus(BaseModel):
    """System status information"""

    model_status: str
    current_model: str
    tools_count: int
    active_sessions: int
    uptime: Optional[str] = None


class ModelInfo(BaseModel):
    """Model information"""

    name: str
    size: str
    description: str
    is_available: bool = True


class SessionInfo(BaseModel):
    """Session information"""

    session_id: str
    created_at: datetime
    last_activity: datetime
    message_count: int
    task_count: int


class AgentAPIInterface:
    """
    Abstract interface for AutoAgent API operations.

    This interface defines the standard methods that should be implemented
    by both terminal and web interfaces to ensure consistency and AutoGen compatibility.
    """

    async def start_conversation(
        self, request: ConversationRequest
    ) -> ConversationResponse:
        """
        Start an AutoGen-compatible conversation.

        Args:
            request: Conversation request with messages and configuration

        Returns:
            ConversationResponse: Conversation results with message history
        """
        raise NotImplementedError

    async def process_task(self, request: TaskRequest) -> TaskResponse:
        """
        Process a legacy task request (for backward compatibility).

        Args:
            request: Task request containing task description and metadata

        Returns:
            TaskResponse: Structured response with execution results
        """
        raise NotImplementedError

    async def get_system_status(self) -> SystemStatus:
        """Get current system status"""
        raise NotImplementedError

    async def get_available_tools(self) -> List[ToolInfo]:
        """Get list of available tools"""
        raise NotImplementedError

    async def get_available_models(self) -> List[ModelInfo]:
        """Get list of available models"""
        raise NotImplementedError

    async def get_session_info(self, session_id: Optional[str] = None) -> SessionInfo:
        """Get session information"""
        raise NotImplementedError

    async def clear_session(self, session_id: Optional[str] = None) -> bool:
        """Clear session data"""
        raise NotImplementedError


class APIResponse:
    """Utility class for creating standardized API responses"""

    @staticmethod
    def success(data: Any = None, message: str = "Success") -> Dict[str, Any]:
        """Create success response"""
        return {
            "success": True,
            "message": message,
            "data": data,
            "timestamp": datetime.now().isoformat(),
        }

    @staticmethod
    def error(
        message: str, error_code: Optional[str] = None, details: Any = None
    ) -> Dict[str, Any]:
        """Create error response"""
        return {
            "success": False,
            "message": message,
            "error_code": error_code,
            "details": details,
            "timestamp": datetime.now().isoformat(),
        }

    @staticmethod
    def task_result(task_response: TaskResponse) -> Dict[str, Any]:
        """Create task execution result response"""
        return {
            "success": task_response.success,
            "task_id": task_response.task_id,
            "message": task_response.message,
            "summary": task_response.summary,
            "steps": [step.model_dump() for step in task_response.steps],
            "related_tasks": task_response.related_tasks,
            "metadata": task_response.metadata,
            "error": task_response.error,
            "timestamp": datetime.now().isoformat(),
        }


def create_task_step(
    step_id: Union[str, int],
    tool: str,
    action: str,
    parameters: Dict[str, Any],
    description: str,
    status: TaskStatus = TaskStatus.PENDING,
) -> TaskStep:
    """Utility function to create a TaskStep"""
    return TaskStep(
        id=step_id,
        tool=tool,
        action=action,
        parameters=parameters,
        description=description,
        status=status,
    )


def create_tool_info(
    name: str,
    description: str,
    category: str = "general",
    actions: List[str] = None,
    parameters: Dict[str, Any] = None,
) -> ToolInfo:
    """Utility function to create ToolInfo"""
    return ToolInfo(
        name=name,
        description=description,
        category=category,
        actions=actions or [],
        parameters=parameters or {},
    )
