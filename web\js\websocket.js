// WebSocket连接管理模块
class WebSocketManager {
    constructor(messageHandler) {
        this.websocket = null;
        this.reconnectAttempts = 0;
        this.messageHandler = messageHandler;
        this.maxReconnectAttempts = 5;
    }

    // 初始化WebSocket连接
    initialize() {
        // 如果已经有连接，先关闭
        if (this.websocket) {
            this.websocket.close();
        }

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;

        console.log('正在连接WebSocket:', wsUrl);
        this.updateConnectionStatus('connecting');

        this.websocket = new WebSocket(wsUrl);

        this.websocket.onopen = () => {
            console.log('WebSocket连接已建立');
            this.updateConnectionStatus('connected');
            this.showNotification('模型连接成功', 'success');
            // 重置重连计数器
            this.reconnectAttempts = 0;
        };

        this.websocket.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                this.messageHandler(message);
            } catch (error) {
                console.error('解析WebSocket消息失败:', error);
            }
        };

        this.websocket.onclose = (event) => {
            console.log('WebSocket连接已关闭, 代码:', event.code, '原因:', event.reason);
            this.updateConnectionStatus('disconnected');

            // 如果不是手动关闭，尝试重连
            if (event.code !== 1000) {
                this.attemptReconnect();
            }
        };

        this.websocket.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.updateConnectionStatus('error');
        };
    }

    // 尝试重连
    attemptReconnect() {
        if (!this.reconnectAttempts) {
            this.reconnectAttempts = 0;
        }

        this.reconnectAttempts++;
        const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts - 1), 30000); // 指数退避，最大30秒

        if (this.reconnectAttempts <= this.maxReconnectAttempts) {
            console.log(`第 ${this.reconnectAttempts} 次重连尝试，${delay/1000}秒后重试...`);
            this.showNotification(`连接断开，${delay/1000}秒后重试 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`, 'warning');

            setTimeout(() => {
                this.initialize();
            }, delay);
        } else {
            console.error('WebSocket重连失败，已达到最大重试次数');
            this.showNotification('连接失败，请刷新页面重试', 'error');
        }
    }

    // 发送消息
    send(message) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify(message));
        } else {
            console.error('WebSocket未连接，无法发送消息');
        }
    }

    // 关闭连接
    close() {
        if (this.websocket) {
            this.websocket.close(1000, 'Manual close');
        }
    }

    // 更新连接状态显示
    updateConnectionStatus(status) {
        const statusElement = document.querySelector('#model-status .status-dot');

        if (statusElement) {
            statusElement.className = 'status-dot';
            statusElement.classList.add(status);
        }

        // 不在这里更新模型名称，模型名称由AgentChat类管理
        // 只更新连接状态相关的UI
    }

    // 显示通知
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 添加关闭事件
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.remove();
        });

        // 自动关闭
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);

        // 添加显示动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
    }

    // 获取连接状态
    getConnectionState() {
        if (!this.websocket) return 'disconnected';

        switch (this.websocket.readyState) {
            case WebSocket.CONNECTING:
                return 'connecting';
            case WebSocket.OPEN:
                return 'connected';
            case WebSocket.CLOSING:
                return 'disconnecting';
            case WebSocket.CLOSED:
                return 'disconnected';
            default:
                return 'unknown';
        }
    }

    // 检查连接是否可用
    isConnected() {
        return this.websocket && this.websocket.readyState === WebSocket.OPEN;
    }
}

// 导出WebSocket管理器
window.WebSocketManager = WebSocketManager;
