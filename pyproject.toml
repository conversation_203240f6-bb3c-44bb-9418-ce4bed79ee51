[project]
name = "agent"
version = "0.1.0"
description = "Autonomous Task Planning and Execution Demo"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "httpx>=0.25.0",
    "pydantic>=2.0.0",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "sse-starlette>=1.6.0",
    "websockets>=12.0",
    "rich>=13.0.0",
    "asyncio-mqtt>=0.16.0",
    "beautifulsoup4>=4.12.0",
    "duckduckgo-search>=4.0.0",
    "aiohttp>=3.12.2",
]

[[tool.uv.index]]
url = "https://pypi.mirrors.ustc.edu.cn/simple"
default = true
