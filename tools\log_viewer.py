#!/usr/bin/env python3
"""
任务日志查看器

这个工具用于查看和分析按任务ID组织的日志文件。

使用方法:
    python tools/log_viewer.py                    # 列出所有任务
    python tools/log_viewer.py <task_id>          # 查看特定任务的日志
    python tools/log_viewer.py <task_id> --type llm  # 查看特定类型的日志
"""

import sys
import json
from pathlib import Path
from datetime import datetime
from typing import Optional

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from cyberagent.core.task_logger import TaskLoggerManager


def format_timestamp(timestamp_str: str) -> str:
    """格式化时间戳"""
    try:
        dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    except:
        return timestamp_str


def list_all_tasks():
    """列出所有任务"""
    print("📋 任务日志列表")
    print("=" * 80)
    
    task_logs = TaskLoggerManager.list_task_logs()
    
    if not task_logs:
        print("❌ 没有找到任务日志")
        return
    
    print(f"📊 共找到 {len(task_logs)} 个任务")
    print()
    
    for i, task in enumerate(task_logs, 1):
        task_id = task.get('task_id', '未知')
        description = task.get('task_description', '无描述')
        start_time = format_timestamp(task.get('start_time', ''))
        status = task.get('status', '未知')
        duration = task.get('duration_seconds', 0)
        
        status_emoji = {
            'running': '🔄',
            'completed': '✅', 
            'failed': '❌'
        }.get(status, '❓')
        
        print(f"{i:2d}. {status_emoji} {task_id}")
        print(f"    📝 描述: {description[:60]}{'...' if len(description) > 60 else ''}")
        print(f"    ⏰ 开始: {start_time}")
        if duration > 0:
            print(f"    ⏱️  耗时: {duration:.2f}秒")
        print(f"    📁 日志: logs/tasks/{task_id}/")
        print()


def view_task_logs(task_id: str, log_type: Optional[str] = None):
    """查看特定任务的日志"""
    task_dir = TaskLoggerManager.get_task_log_path(task_id)
    
    if not task_dir:
        print(f"❌ 任务 {task_id} 的日志目录不存在")
        return
    
    # 读取元数据
    metadata_file = task_dir / "metadata.json"
    if metadata_file.exists():
        with open(metadata_file, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        print(f"📋 任务信息: {task_id}")
        print("=" * 80)
        print(f"📝 描述: {metadata.get('task_description', '无描述')}")
        print(f"⏰ 开始: {format_timestamp(metadata.get('start_time', ''))}")
        if metadata.get('end_time'):
            print(f"🏁 结束: {format_timestamp(metadata.get('end_time', ''))}")
        if metadata.get('duration_seconds'):
            print(f"⏱️  耗时: {metadata.get('duration_seconds', 0):.2f}秒")
        
        status = metadata.get('status', '未知')
        status_emoji = {
            'running': '🔄',
            'completed': '✅', 
            'failed': '❌'
        }.get(status, '❓')
        print(f"📊 状态: {status_emoji} {status}")
        
        if metadata.get('summary'):
            print(f"📄 总结: {metadata.get('summary')}")
        print()
    
    # 显示可用的日志文件
    log_files = {
        'full': task_dir / 'full.log',
        'llm': task_dir / 'llm.log',
        'execution': task_dir / 'execution.log', 
        'api': task_dir / 'api.log'
    }
    
    available_logs = {k: v for k, v in log_files.items() if v.exists()}
    
    if not available_logs:
        print("❌ 没有找到日志文件")
        return
    
    print("📁 可用日志文件:")
    for log_name, log_file in available_logs.items():
        size = log_file.stat().st_size
        size_str = f"{size}B" if size < 1024 else f"{size/1024:.1f}KB"
        print(f"  📄 {log_name}.log ({size_str})")
    print()
    
    # 显示指定类型的日志或完整日志
    if log_type and log_type in available_logs:
        log_file = available_logs[log_type]
        print(f"📖 {log_type}.log 内容:")
        print("-" * 80)
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(content)
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
    elif log_type:
        print(f"❌ 日志类型 '{log_type}' 不存在")
        print(f"💡 可用类型: {', '.join(available_logs.keys())}")
    else:
        # 显示完整日志
        full_log = available_logs.get('full')
        if full_log:
            print("📖 完整日志内容:")
            print("-" * 80)
            try:
                with open(full_log, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(content)
            except Exception as e:
                print(f"❌ 读取日志文件失败: {e}")
        else:
            print("❌ 完整日志文件不存在")


def main():
    """主函数"""
    if len(sys.argv) == 1:
        # 没有参数，列出所有任务
        list_all_tasks()
    elif len(sys.argv) == 2:
        # 一个参数，查看特定任务的完整日志
        task_id = sys.argv[1]
        view_task_logs(task_id)
    elif len(sys.argv) == 4 and sys.argv[2] == '--type':
        # 三个参数，查看特定任务的特定类型日志
        task_id = sys.argv[1]
        log_type = sys.argv[3]
        view_task_logs(task_id, log_type)
    else:
        print("❌ 参数错误")
        print()
        print("使用方法:")
        print("  python tools/log_viewer.py                    # 列出所有任务")
        print("  python tools/log_viewer.py <task_id>          # 查看特定任务的日志")
        print("  python tools/log_viewer.py <task_id> --type <type>  # 查看特定类型的日志")
        print()
        print("日志类型: full, llm, execution, api")


if __name__ == "__main__":
    main()
