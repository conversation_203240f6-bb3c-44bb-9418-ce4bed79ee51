"""
File Operations Tool - Handles file system operations
"""

import os
import asyncio
from pathlib import Path
from typing import Any, Dict

from ..base import <PERSON><PERSON><PERSON><PERSON>, ToolMetadata, ToolParameter


class FileOperationsTool(MCPTool):
    """Tool for file system operations"""

    def get_metadata(self) -> ToolMetadata:
        return ToolMetadata(
            name="file_ops",
            description="Perform file system operations like read, write, list, create directories",
            category="filesystem",
            parameters=[
                ToolParameter(
                    name="operation",
                    type="string",
                    description="Operation to perform: read, write, list, mkdir, delete",
                    required=True,
                ),
                ToolParameter(
                    name="path",
                    type="string",
                    description="File or directory path",
                    required=True,
                ),
                ToolParameter(
                    name="content",
                    type="string",
                    description="Content to write (for write operation)",
                    required=False,
                ),
                ToolParameter(
                    name="encoding",
                    type="string",
                    description="File encoding",
                    required=False,
                    default="utf-8",
                ),
            ],
        )

    async def execute_locally(self, action: str, parameters: Dict[str, Any]) -> Any:
        """Execute file operations locally"""

        operation = parameters.get("operation")
        path = Path(parameters.get("path"))
        content = parameters.get("content", "")
        encoding = parameters.get("encoding", "utf-8")

        try:
            if operation == "read":
                return await self._read_file(path, encoding)
            elif operation == "write":
                return await self._write_file(path, content, encoding)
            elif operation == "list":
                return await self._list_directory(path)
            elif operation == "mkdir":
                return await self._create_directory(path)
            elif operation == "delete":
                return await self._delete_path(path)
            else:
                raise ValueError(f"Unknown operation: {operation}")

        except Exception as e:
            return f"Error: {str(e)}"

    async def _read_file(self, path: Path, encoding: str) -> str:
        """Read file content"""
        if not path.exists():
            raise FileNotFoundError(f"File not found: {path}")

        if not path.is_file():
            raise ValueError(f"Path is not a file: {path}")

        # Simulate async operation
        await asyncio.sleep(0.1)

        with open(path, "r", encoding=encoding) as f:
            content = f.read()

        return f"File content ({len(content)} characters):\n{content[:500]}{'...' if len(content) > 500 else ''}"

    async def _write_file(self, path: Path, content: str, encoding: str) -> str:
        """Write content to file"""
        # Create parent directories if they don't exist
        path.parent.mkdir(parents=True, exist_ok=True)

        # Simulate async operation
        await asyncio.sleep(0.1)

        with open(path, "w", encoding=encoding) as f:
            f.write(content)

        return f"Successfully wrote {len(content)} characters to {path}"

    async def _list_directory(self, path: Path) -> str:
        """List directory contents"""
        if not path.exists():
            raise FileNotFoundError(f"Directory not found: {path}")

        if not path.is_dir():
            raise ValueError(f"Path is not a directory: {path}")

        # Simulate async operation
        await asyncio.sleep(0.1)

        items = []
        for item in path.iterdir():
            item_type = "DIR" if item.is_dir() else "FILE"
            size = item.stat().st_size if item.is_file() else "-"
            items.append(f"{item_type:4} {size:>10} {item.name}")

        return f"Directory listing for {path}:\n" + "\n".join(items)

    async def _create_directory(self, path: Path) -> str:
        """Create directory"""
        # Simulate async operation
        await asyncio.sleep(0.1)

        path.mkdir(parents=True, exist_ok=True)
        return f"Successfully created directory: {path}"

    async def _delete_path(self, path: Path) -> str:
        """Delete file or directory"""
        if not path.exists():
            raise FileNotFoundError(f"Path not found: {path}")

        # Simulate async operation
        await asyncio.sleep(0.1)

        if path.is_file():
            path.unlink()
            return f"Successfully deleted file: {path}"
        elif path.is_dir():
            # Only delete empty directories for safety
            try:
                path.rmdir()
                return f"Successfully deleted directory: {path}"
            except OSError:
                return f"Directory not empty, cannot delete: {path}"
        else:
            raise ValueError(f"Unknown path type: {path}")
