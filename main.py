"""
AutoAgent - Autonomous Task Planning and Execution Framework

This is the main entry point for the AutoAgent framework, providing
unified access to both terminal and web interfaces.
"""

import asyncio
import sys
import argparse
from pathlib import Path
from typing import Optional

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from cyberagent.core.agent import CyberAgent
from cyberagent.interfaces.api_server import Agent<PERSON><PERSON>


def create_argument_parser():
    """Create command line argument parser"""
    parser = argparse.ArgumentParser(
        description="AutoAgent - Autonomous Task Planning and Execution Framework",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    # Start interactive terminal mode
  python main.py --web              # Start web interface
  python main.py --mcp              # Start MCP server
  python main.py --web --port 8080  # Start web interface on port 8080
        """,
    )

    parser.add_argument(
        "--web",
        action="store_true",
        help="Start web interface instead of terminal mode",
    )

    parser.add_argument(
        "--mcp", action="store_true", help="Start MCP (Model Context Protocol) server"
    )

    parser.add_argument(
        "--port", type=int, default=8001, help="Port for web interface (default: 8001)"
    )

    parser.add_argument(
        "--host",
        type=str,
        default="127.0.0.1",
        help="Host for web interface (default: 127.0.0.1)",
    )

    parser.add_argument(
        "--model", type=str, help="Override default model (e.g., qwen3:32b)"
    )

    parser.add_argument("--config", type=str, help="Path to configuration file")

    return parser


async def start_terminal_mode(
    model: Optional[str] = None, config: Optional[str] = None
):
    """Start interactive terminal mode"""
    print("🤖 Starting AutoAgent Terminal Mode...")

    agent = CyberAgent(model=model, config_path=config)
    await agent.start_interactive_mode()


def start_web_mode(
    host: str = "127.0.0.1", port: int = 8001, model: Optional[str] = None
):
    """Start web interface mode"""
    print(f"🌐 Starting AutoAgent Web Interface on {host}:{port}...")

    api = AgentAPI(model=model)
    api.run(host=host, port=port)


def start_mcp_mode():
    """Start MCP server mode"""
    print("🔗 Starting AutoAgent MCP Server...")

    from cyberagent.interfaces.mcp_server import main as start_mcp_server

    start_mcp_server()


def main():
    """Main entry point"""
    parser = create_argument_parser()
    args = parser.parse_args()

    try:
        if args.mcp:
            start_mcp_mode()
        elif args.web:
            start_web_mode(host=args.host, port=args.port, model=args.model)
        else:
            asyncio.run(start_terminal_mode(model=args.model, config=args.config))
    except KeyboardInterrupt:
        print("\n👋 AutoAgent stopped by user")
    except Exception as e:
        print(f"💥 Error starting AutoAgent: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
