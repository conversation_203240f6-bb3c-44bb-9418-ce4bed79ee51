// 配置管理模块
class ConfigManager {
    constructor() {
        this.cotModels = [];
        this.debugMode = false;
        this.currentModel = 'qwen3:32b';
        this.availableModels = [];
    }

    // 加载配置信息
    async loadConfig() {
        try {
            const response = await fetch('/api/config');
            const result = await response.json();

            if (result.success && result.config) {
                this.cotModels = result.config.cot_models || [];
                this.debugMode = result.config.debug_mode || false;
                this.currentModel = result.config.default_model || 'qwen3:32b';

                if (this.debugMode) {
                    console.log('🔧 调试模式已启用');
                    console.log('📋 COT模型列表:', this.cotModels);
                } else {
                    console.log('🔧 调试模式已禁用，控制台输出将被精简');
                }
            } else {
                console.warn('使用默认配置，原因:', result.error || '无法获取配置');
                // 使用默认配置
                this.cotModels = ['qwen3:32b', 'qwq:latest', 'deepseek-r1:32b'];
                this.debugMode = false;
            }
        } catch (error) {
            console.error('加载配置失败:', error);
            // 使用默认配置
            this.cotModels = ['qwen3:32b', 'qwq:latest', 'deepseek-r1:32b'];
            this.debugMode = false;
        }
    }

    // 加载可用模型列表
    async loadAvailableModels() {
        try {
            const response = await fetch('/api/models');
            const result = await response.json();

            if (result.success && result.models && result.models.length > 0) {
                this.availableModels = result.models;
                if (this.debugMode) {
                    console.log('已从ollama服务器加载模型列表:', result.models.length, '个模型');
                }
                return result.models;
            } else {
                if (this.debugMode) {
                    console.warn('使用默认模型列表，原因:', result.error || '无法获取模型');
                }
                // 使用默认模型列表
                this.availableModels = [
                    { name: 'qwen3:32b', description: 'Qwen3 (32B)' },
                    { name: 'gemma3:27b', description: 'Gemma3 (27B)' },
                    { name: 'qwq:latest', description: 'QWQ (latest)' },
                    { name: 'deepseek-r1:32b', description: 'DeepSeek-R1 (32B)' }
                ];
                return this.availableModels;
            }
        } catch (error) {
            if (this.debugMode) {
                console.error('加载模型列表失败:', error);
            }
            // 返回默认模型列表
            this.availableModels = [
                { name: 'qwen3:32b', description: 'Qwen3 (32B)' },
                { name: 'gemma3:27b', description: 'Gemma3 (27B)' },
                { name: 'qwq:latest', description: 'QWQ (latest)' },
                { name: 'deepseek-r1:32b', description: 'DeepSeek-R1 (32B)' }
            ];
            return this.availableModels;
        }
    }

    // 检查是否为COT模型
    isCOTModel(modelName = null) {
        const model = modelName || this.currentModel;
        return this.cotModels.some(cotModel =>
            model.toLowerCase().includes(cotModel.toLowerCase())
        );
    }

    // 获取模型显示名称
    getModelDisplayName(modelValue) {
        const modelNames = {
            'qwen3:32b': 'Qwen3 (32B)',
            'gemma3:27b': 'Gemma3 (27B)',
            'qwq:latest': 'QWQ (latest)',
            'deepseek-r1:32b': 'DeepSeek-R1 (32B)'
        };
        return modelNames[modelValue] || modelValue;
    }

    // 处理think标签内容
    processThinkContent(content) {
        const isCOT = this.isCOTModel();
        if (this.debugMode) {
            console.log('[COT] 处理think标签 - 模型:', this.currentModel, '是否COT:', isCOT);
        }

        if (!content) {
            if (this.debugMode) {
                console.log('[COT] 内容为空，返回原内容');
            }
            return content;
        }

        if (!isCOT) {
            if (this.debugMode) {
                console.log('[COT] 非COT模型，返回原内容');
            }
            return content; // 非COT模型直接返回原内容
        }

        // 检查是否包含think标签
        const hasThinkTags = content.includes('<think>') && content.includes('</think>');
        if (this.debugMode) {
            console.log('[COT] 内容包含think标签:', hasThinkTags);
        }

        // 提取think标签内容
        const thinkRegex = /<think>([\s\S]*?)<\/think>/gi;
        const thinkMatches = [];
        let match;

        while ((match = thinkRegex.exec(content)) !== null) {
            thinkMatches.push({
                fullMatch: match[0],
                content: match[1].trim()
            });
        }

        if (this.debugMode) {
            console.log('[COT] 找到think标签数量:', thinkMatches.length);
        }

        if (thinkMatches.length === 0) {
            if (this.debugMode) {
                console.log('[COT] 没有找到think标签，返回原内容');
            }
            return content; // 没有think标签，直接返回
        }

        // 移除think标签，保留其他内容
        let processedContent = content;
        thinkMatches.forEach(thinkMatch => {
            processedContent = processedContent.replace(thinkMatch.fullMatch, '');
        });

        // 清理多余的空行
        processedContent = processedContent.replace(/\n\s*\n\s*\n/g, '\n\n').trim();

        const result = {
            content: processedContent,
            thinkingSteps: thinkMatches.map(match => ({
                content: match.content
            }))
        };

        if (this.debugMode) {
            console.log('[COT] 处理结果 - 思考步骤数:', result.thinkingSteps.length, '处理后内容长度:', processedContent.length);
        }

        return result;
    }
}

// 导出配置管理器
window.ConfigManager = ConfigManager;
