// 会话管理模块
class SessionManager {
    constructor() {
        this.sessions = new Map();
        this.currentSessionId = null;
        this.messageId = 0;
    }

    // 创建新会话
    createNewSession() {
        const sessionId = `session_${Date.now()}`;
        const session = {
            id: sessionId,
            title: '新对话',
            messages: [],
            createdAt: new Date(),
            lastActivity: new Date()
        };

        this.sessions.set(sessionId, session);
        this.currentSessionId = sessionId;

        this.updateSessionsList();
        this.updateChatHeader();
        
        return session;
    }

    // 切换会话
    switchToSession(sessionId) {
        if (this.sessions.has(sessionId)) {
            this.currentSessionId = sessionId;
            this.updateSessionsList();
            this.updateChatHeader();
            return this.sessions.get(sessionId);
        }
        return null;
    }

    // 删除当前会话
    clearCurrentSession() {
        if (this.currentSessionId && this.sessions.has(this.currentSessionId)) {
            // 删除当前会话
            this.sessions.delete(this.currentSessionId);

            // 如果还有其他会话，切换到最新的会话
            if (this.sessions.size > 0) {
                const sessionsArray = Array.from(this.sessions.values())
                    .sort((a, b) => b.lastActivity - a.lastActivity);
                this.currentSessionId = sessionsArray[0].id;
                this.updateSessionsList();
                this.updateChatHeader();
                return sessionsArray[0];
            } else {
                // 如果没有其他会话，创建新会话
                return this.createNewSession();
            }
        }
        return null;
    }

    // 添加消息
    addMessage(role, content, metadata = {}) {
        if (!this.currentSessionId) return null;

        const session = this.sessions.get(this.currentSessionId);
        const message = {
            id: ++this.messageId,
            role: role,
            content: content,
            timestamp: new Date(),
            metadata: metadata
        };

        session.messages.push(message);
        session.lastActivity = new Date();

        // 更新会话标题（使用第一条用户消息）
        if (role === 'user' && session.title === '新对话') {
            session.title = this.generateSessionTitle(content);
        }

        this.updateSessionsList();
        this.updateChatHeader();

        return message;
    }

    // 获取当前会话
    getCurrentSession() {
        if (this.currentSessionId) {
            return this.sessions.get(this.currentSessionId);
        }
        return null;
    }

    // 获取当前会话的消息
    getCurrentMessages() {
        const session = this.getCurrentSession();
        return session ? session.messages : [];
    }

    // 生成会话标题
    generateSessionTitle(content) {
        // 清理消息内容，移除多余的空格和换行
        const cleanContent = content.trim().replace(/\s+/g, ' ');

        // 如果消息太短，直接返回
        if (cleanContent.length <= 12) {
            return cleanContent;
        }

        // 智能提取关键词和主题
        return this.extractSmartTitle(cleanContent);
    }

    // 智能标题提取
    extractSmartTitle(content) {
        // 常见问题模式识别
        const patterns = [
            { regex: /^(计算|求|算)(.{1,15})/, template: '计算$2' },
            { regex: /^(搜索|查找|找)(.{1,15})/, template: '搜索$2' },
            { regex: /^(创建|写|生成)(.{1,15})/, template: '创建$2' },
            { regex: /^(帮我|请)(.{1,15})/, template: '$2' },
            { regex: /^(.{1,18})/, template: '$1' }
        ];

        for (const pattern of patterns) {
            const match = content.match(pattern.regex);
            if (match) {
                let title = pattern.template.replace(/\$(\d+)/g, (_, num) => {
                    const group = match[parseInt(num)];
                    return group ? group.trim() : '';
                });

                // 清理标题
                title = title.replace(/[？！。，、]/g, '').trim();

                // 限制长度
                if (title.length > 16) {
                    title = title.substring(0, 16);
                    // 在合适位置截断
                    const lastSpace = title.lastIndexOf(' ');
                    if (lastSpace > 8) {
                        title = title.substring(0, lastSpace);
                    }
                }

                return title + (content.length > title.length ? '...' : '');
            }
        }

        // 默认截断
        return content.substring(0, 16) + '...';
    }

    // 更新会话列表
    updateSessionsList() {
        const sessionsList = document.getElementById('sessions-list');
        if (!sessionsList) return;

        const sessionsArray = Array.from(this.sessions.values())
            .sort((a, b) => b.lastActivity - a.lastActivity);

        sessionsList.innerHTML = sessionsArray.map(session => {
            const isActive = session.id === this.currentSessionId;
            const lastMessage = session.messages[session.messages.length - 1];
            const preview = lastMessage ?
                (lastMessage.role === 'user' ? lastMessage.content : '助手回复') :
                '开始新对话';

            return `
                <div class="session-item ${isActive ? 'active' : ''}" data-session-id="${session.id}">
                    <div class="session-header">
                        <div class="session-title">${session.title}</div>
                        <div class="session-time">${this.formatTime(session.lastActivity)}</div>
                    </div>
                    <div class="session-preview">${preview.substring(0, 50)}${preview.length > 50 ? '...' : ''}</div>
                </div>
            `;
        }).join('');
    }

    // 更新聊天头部
    updateChatHeader() {
        const chatHeader = document.querySelector('.chat-header h2');
        if (!chatHeader) return;

        const session = this.getCurrentSession();
        if (session) {
            chatHeader.textContent = session.title;
        }
    }

    // 格式化时间
    formatTime(date) {
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;
        
        return date.toLocaleDateString();
    }

    // 获取所有会话
    getAllSessions() {
        return Array.from(this.sessions.values());
    }

    // 清空所有会话
    clearAllSessions() {
        this.sessions.clear();
        this.currentSessionId = null;
        this.messageId = 0;
        this.updateSessionsList();
        this.updateChatHeader();
    }

    // 导出会话数据
    exportSessions() {
        const sessionsData = {
            sessions: Array.from(this.sessions.entries()),
            currentSessionId: this.currentSessionId,
            messageId: this.messageId,
            exportTime: new Date().toISOString()
        };
        return JSON.stringify(sessionsData, null, 2);
    }

    // 导入会话数据
    importSessions(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            this.sessions = new Map(data.sessions);
            this.currentSessionId = data.currentSessionId;
            this.messageId = data.messageId || 0;
            this.updateSessionsList();
            this.updateChatHeader();
            return true;
        } catch (error) {
            console.error('导入会话数据失败:', error);
            return false;
        }
    }
}

// 导出会话管理器
window.SessionManager = SessionManager;
