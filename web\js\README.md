# AgentChat 前端模块化架构

## 概述

为了提高代码的可维护性和可读性，我们将原来的单一 `script.js` 文件（3000+行）拆分成了多个功能模块。每个模块负责特定的功能，模块之间通过清晰的接口进行交互。

## 模块结构

### 1. `config.js` - 配置管理模块
**职责：** 管理应用配置、模型列表、COT处理等
- `ConfigManager` 类
- 从后端API获取配置信息
- 管理COT模型列表
- 处理think标签内容
- 调试模式控制

### 2. `websocket.js` - WebSocket连接管理模块
**职责：** 管理WebSocket连接、重连机制、连接状态
- `WebSocketManager` 类
- 自动重连机制
- 连接状态管理
- 消息发送和接收
- 通知系统

### 3. `session.js` - 会话管理模块
**职责：** 管理聊天会话、消息存储、会话切换
- `SessionManager` 类
- 会话创建、切换、删除
- 消息添加和管理
- 会话标题生成
- 会话数据导入导出

### 4. `message-renderer.js` - 消息渲染模块
**职责：** 渲染各种类型的消息、工具执行结果等
- `MessageRenderer` 类
- 用户消息渲染
- 助手消息渲染
- 工具执行结果渲染
- 规划步骤渲染
- 思维过程渲染
- 打字机效果

### 5. `event-handler.js` - 事件处理模块
**职责：** 处理用户交互事件、键盘快捷键等
- `EventHandler` 类
- 发送消息事件
- 输入框事件处理
- 会话切换事件
- 可折叠区域事件
- 键盘快捷键

### 6. `message-handler.js` - WebSocket消息处理模块
**职责：** 处理来自后端的WebSocket消息
- `MessageHandler` 类
- 各种消息类型的处理
- 规划完成处理
- 工具调用处理
- 执行完成处理
- 参考文献提取

### 7. `agent-chat.js` - 主应用类
**职责：** 整合所有模块，提供统一的API接口
- `AgentChat` 类
- 模块初始化和协调
- 消息发送流程
- UI更新协调
- 用户友好回答生成

### 8. `main.js` - 应用入口
**职责：** 应用启动、依赖检查、错误处理
- 应用初始化
- 依赖检查
- 错误处理和显示
- 开发模式调试功能

## 模块依赖关系

```
main.js
  └── AgentChat (agent-chat.js)
      ├── ConfigManager (config.js)
      ├── SessionManager (session.js)
      ├── MessageRenderer (message-renderer.js)
      ├── MessageHandler (message-handler.js)
      ├── EventHandler (event-handler.js)
      └── WebSocketManager (websocket.js)
```

## 加载顺序

HTML中的JavaScript文件必须按以下顺序加载：

1. `config.js` - 配置管理（基础依赖）
2. `websocket.js` - WebSocket管理
3. `session.js` - 会话管理
4. `message-renderer.js` - 消息渲染
5. `event-handler.js` - 事件处理
6. `message-handler.js` - 消息处理
7. `agent-chat.js` - 主应用类
8. `main.js` - 应用入口（最后加载）

## 主要改进

### 1. 代码组织
- **单一职责原则：** 每个模块只负责特定功能
- **清晰的接口：** 模块间通过明确的API交互
- **易于维护：** 修改某个功能只需要关注对应模块

### 2. 调试和开发
- **调试模式：** 统一的调试输出控制
- **开发工具：** 开发模式下提供调试快捷键和工具
- **错误处理：** 更好的错误捕获和显示

### 3. 性能优化
- **按需加载：** 模块化结构便于后续实现按需加载
- **内存管理：** 更好的资源清理机制
- **事件管理：** 统一的事件绑定和解绑

## 开发调试

### 调试模式
在开发环境中，可以使用以下快捷键：
- `Ctrl+Shift+D` - 切换调试模式
- `Ctrl+Shift+R` - 重新初始化应用

### 调试工具
在浏览器控制台中可以使用：
```javascript
// 获取配置信息
window.debugAgentChat.getConfig()

// 获取所有会话
window.debugAgentChat.getSessions()

// 获取WebSocket状态
window.debugAgentChat.getWebSocketState()

// 导出会话数据
window.debugAgentChat.exportSessions()

// 导入会话数据
window.debugAgentChat.importSessions(jsonData)

// 清空所有会话
window.debugAgentChat.clearAllSessions()

// 切换调试模式
window.debugAgentChat.toggleDebugMode()
```

## 配置管理

### 环境变量配置
在 `.env` 文件中配置：
```env
# COT模型列表
COT_MODELS=qwen3:32b,qwen2.5-coder,deepseek-coder,claude,gpt-4,qwq:latest,qwq,deepseek-r1:32b,deepseek-r1:70b,deepseek-r1,o1-preview,o1-mini,o1

# 前端调试模式
FRONTEND_DEBUG_MODE=false
```

### API端点
- `GET /api/config` - 获取前端配置信息
- `GET /api/models` - 获取可用模型列表
- `POST /api/task` - 发送任务请求

## 未来扩展

这种模块化架构为未来的功能扩展提供了良好的基础：

1. **插件系统：** 可以轻松添加新的工具模块
2. **主题系统：** 可以独立管理UI主题
3. **国际化：** 可以添加独立的语言模块
4. **离线支持：** 可以添加缓存和离线功能模块
5. **性能监控：** 可以添加性能监控模块

## 注意事项

1. **加载顺序：** 必须严格按照依赖关系加载JavaScript文件
2. **全局变量：** 每个模块都会在 `window` 对象上暴露对应的类
3. **错误处理：** 如果某个模块加载失败，应用会显示错误信息
4. **浏览器兼容性：** 使用了ES6+语法，需要现代浏览器支持

## 迁移说明

从原来的 `script.js` 迁移到新的模块化结构：

1. **HTML更新：** 更新script标签引用新的模块文件
2. **配置迁移：** COT模型列表现在统一在后端配置
3. **调试输出：** 大部分调试信息已被精简或移至调试模式
4. **API兼容：** 主要的API接口保持兼容，但内部实现已重构
