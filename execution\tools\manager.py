"""
Tool Manager - Manages and provides access to all available tools
"""

from typing import Dict, List, Optional
from .base import BaseTool, ToolMetadata
from .general.file_ops import FileOperationsTool
from .general.web_search import WebSearchTool
from .general.calculator import CalculatorTool
from .general.web_fetch import WebFetchTool


class ToolManager:
    """
    Manages all available tools and provides a unified interface for tool execution
    """

    def __init__(self):
        self.tools: Dict[str, BaseTool] = {}
        self._register_default_tools()

    def _register_default_tools(self):
        """Register the default set of tools"""
        default_tools = [
            FileOperationsTool(),
            WebSearchTool(),
            CalculatorTool(),
            WebFetchTool(),
        ]

        for tool in default_tools:
            self.register_tool(tool)

    def register_tool(self, tool: BaseTool):
        """Register a new tool"""
        tool_name = tool.metadata.name
        self.tools[tool_name] = tool
        print(f"🔧 已注册工具: {tool_name}")

    def get_tool(self, tool_name: str) -> Optional[BaseTool]:
        """Get a tool by name"""
        return self.tools.get(tool_name)

    def list_tools(self) -> List[ToolMetadata]:
        """List all available tools"""
        return [tool.metadata for tool in self.tools.values()]

    def get_tools_by_category(self, category: str) -> List[BaseTool]:
        """Get all tools in a specific category"""
        return [
            tool for tool in self.tools.values() if tool.metadata.category == category
        ]

    async def execute_tool(self, tool_name: str, action: str, parameters: Dict) -> any:
        """Execute a tool with given parameters"""
        tool = self.get_tool(tool_name)
        if not tool:
            raise ValueError(f"Tool '{tool_name}' not found")

        return await tool.execute(action, parameters)

    def get_tool_help(self, tool_name: str) -> Optional[str]:
        """Get help information for a specific tool"""
        tool = self.get_tool(tool_name)
        if not tool:
            return None

        metadata = tool.metadata
        help_text = f"Tool: {metadata.name}\n"
        help_text += f"Description: {metadata.description}\n"
        help_text += f"Category: {metadata.category}\n\n"
        help_text += "Parameters:\n"

        for param in metadata.parameters:
            required_text = "required" if param.required else "optional"
            default_text = (
                f" (default: {param.default})" if param.default is not None else ""
            )
            help_text += f"  - {param.name} ({param.type}, {required_text}): {param.description}{default_text}\n"

        return help_text

    def search_tools(self, query: str) -> List[BaseTool]:
        """Search for tools by name or description"""
        query_lower = query.lower()
        matching_tools = []

        for tool in self.tools.values():
            if (
                query_lower in tool.metadata.name.lower()
                or query_lower in tool.metadata.description.lower()
            ):
                matching_tools.append(tool)

        return matching_tools
