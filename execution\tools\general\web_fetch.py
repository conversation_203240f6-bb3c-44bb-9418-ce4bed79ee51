"""
Web Fetch Tool - Fetches and extracts content from web pages
"""

import asyncio
from typing import Any, Dict
from ..base import <PERSON><PERSON><PERSON><PERSON>, ToolMetadata, ToolParameter


class WebFetchTool(MCPTool):
    """Tool for fetching and extracting content from web pages"""

    def get_metadata(self) -> ToolMetadata:
        return ToolMetadata(
            name="web_fetch",
            description="Fetch and extract content from web pages for detailed analysis",
            category="information",
            parameters=[
                ToolParameter(
                    name="url",
                    type="string",
                    description="URL of the web page to fetch",
                    required=True,
                ),
                ToolParameter(
                    name="max_length",
                    type="integer",
                    description="Maximum content length to extract",
                    required=False,
                    default=3000,
                ),
            ],
        )

    async def execute_locally(self, action: str, parameters: Dict[str, Any]) -> Any:
        """Fetch content from a web page"""

        url = parameters.get("url")
        max_length = parameters.get("max_length", 8000)  # 增加默认长度限制

        if not url:
            return {"error": "URL参数是必需的"}

        try:
            content = await self._fetch_page_content(url, max_length)

            return {
                "url": url,
                "content": content,
                "length": len(content),
                "success": True,
                "summary": f"成功获取网页内容，长度: {len(content)} 字符",
            }

        except Exception as e:
            error_msg = f"获取网页内容失败: {str(e)}"
            print(f"❌ {error_msg}")
            return {"error": error_msg, "success": False}

    async def _fetch_page_content(self, url: str, max_length: int) -> str:
        """获取并解析网页内容"""
        try:
            import httpx
            from bs4 import BeautifulSoup

            # 设置请求头，模拟浏览器
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            }

            async with httpx.AsyncClient(timeout=15.0, follow_redirects=True) as client:
                response = await client.get(url, headers=headers)
                response.raise_for_status()

                # 检查内容类型
                content_type = response.headers.get("content-type", "").lower()
                if "text/html" not in content_type:
                    return f"不支持的内容类型: {content_type}"

                # 解析HTML内容
                soup = BeautifulSoup(response.text, "html.parser")

                # 移除不需要的标签
                for tag in soup(
                    ["script", "style", "nav", "header", "footer", "aside", "iframe"]
                ):
                    tag.decompose()

                # 尝试多种内容选择器
                content_selectors = [
                    "article",
                    "main",
                    ".content",
                    ".article-content",
                    ".post-content",
                    ".entry-content",
                    ".main-content",
                    "#content",
                    ".article-body",
                    ".post-body",
                ]

                main_content = None
                for selector in content_selectors:
                    main_content = soup.select_one(selector)
                    if main_content and main_content.get_text(strip=True):
                        break

                # 如果没找到主要内容区域，使用body
                if not main_content:
                    main_content = soup.find("body")

                if main_content:
                    # 提取文本内容
                    text = main_content.get_text(separator="\n", strip=True)

                    # 清理文本
                    lines = []
                    for line in text.split("\n"):
                        line = line.strip()
                        if line and len(line) > 3:  # 过滤掉太短的行
                            lines.append(line)

                    content = "\n".join(lines)

                    # 增加默认内容长度限制，允许更多内容显示
                    if len(content) > max_length:
                        content = (
                            content[:max_length]
                            + "...\n\n[内容已截断，如需完整内容请增加max_length参数]"
                        )

                    # 添加网页基本信息
                    title = soup.find("title")
                    title_text = title.get_text(strip=True) if title else "无标题"

                    formatted_content = f"网页标题: {title_text}\n"
                    formatted_content += f"网页链接: {url}\n"
                    formatted_content += f"内容长度: {len(content)} 字符\n"
                    formatted_content += "=" * 50 + "\n\n"
                    formatted_content += content

                    return formatted_content
                else:
                    return "无法提取网页内容"

        except ImportError:
            raise Exception(
                "需要安装 httpx 和 beautifulsoup4 库: pip install httpx beautifulsoup4"
            )
        except httpx.TimeoutException:
            raise Exception("请求超时，网页响应时间过长")
        except httpx.HTTPStatusError as e:
            raise Exception(
                f"HTTP错误 {e.response.status_code}: {e.response.reason_phrase}"
            )
        except Exception as e:
            raise Exception(f"网页获取失败: {str(e)}")

    async def get_multiple_pages(
        self, urls: list, max_length: int = 2000
    ) -> Dict[str, str]:
        """批量获取多个网页内容"""
        results = {}

        for url in urls:
            try:
                content = await self._fetch_page_content(url, max_length)
                results[url] = content
            except Exception as e:
                results[url] = f"获取失败: {str(e)}"

        return results
