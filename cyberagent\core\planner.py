"""
Task Planner - Responsible for breaking down user requests into executable steps
"""

import asyncio
from typing import List, Dict, Optional
from dataclasses import dataclass
from enum import Enum

from .model import OllamaClient, Message


class TaskStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class TaskStep:
    id: int
    action: str
    tool: str
    parameters: Dict
    description: str
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[str] = None
    error: Optional[str] = None


@dataclass
class TaskPlan:
    id: str
    description: str
    steps: List[TaskStep]
    status: TaskStatus = TaskStatus.PENDING
    created_at: str = ""


class TaskPlanner:
    """
    Responsible for analyzing user requests and creating detailed execution plans
    """

    def __init__(self, model_client: OllamaClient, websocket_callback=None):
        self.model_client = model_client
        self.plans: Dict[str, TaskPlan] = {}
        self.websocket_callback = websocket_callback

    async def create_plan(self, task_description: str, plan_id: str = None) -> TaskPlan:
        """Create a detailed execution plan for the given task"""

        if plan_id is None:
            plan_id = f"plan_{len(self.plans) + 1}"

        print(
            f"🤔 [任务规划] 正在规划任务: {task_description} (模型: {self.model_client.model})"
        )

        # 使用模型生成计划
        raw_steps = await self.model_client.generate_plan(task_description)
        print(
            f"📋 [规划结果] 模型 {self.model_client.model} 生成了 {len(raw_steps)} 个步骤"
        )

        # 转换为TaskStep对象
        steps = []
        for step_data in raw_steps:
            step = TaskStep(
                id=step_data.get("id", len(steps) + 1),
                action=step_data.get("action", "unknown"),
                tool=step_data.get("tool", "general"),
                parameters=step_data.get("parameters", {}),
                description=step_data.get("description", "无描述"),
            )
            steps.append(step)

        plan = TaskPlan(
            id=plan_id,
            description=task_description,
            steps=steps,
            created_at=asyncio.get_event_loop().time().__str__(),
        )

        self.plans[plan_id] = plan

        print(f"📋 已创建包含 {len(steps)} 个步骤的计划:")
        for i, step in enumerate(steps, 1):
            print(f"  {i}. {step.description} (使用 {step.tool})")

        return plan

    async def create_plan_streaming(
        self, task_description: str, plan_id: str = None, session_id: str = None
    ) -> TaskPlan:
        """Create a detailed execution plan with streaming updates"""

        if plan_id is None:
            plan_id = f"plan_{len(self.plans) + 1}"

        print(
            f"🤔 [流式规划] 正在规划任务: {task_description} (模型: {self.model_client.model})"
        )

        # 发送规划开始通知
        await self._send_websocket_message(
            {
                "type": "planning_thinking",
                "data": {
                    "task_id": plan_id,
                    "session_id": session_id,
                    "message": f"🤔 正在分析任务: {task_description}",
                    "stage": "analysis",
                },
            }
        )

        # 发送思维过程
        thinking_steps = [
            "🎯 理解用户需求和目标",
            "🔍 分析任务的复杂度和依赖关系",
            "🛠️ 选择合适的工具和方法",
            "📋 制定详细的执行步骤",
            "✅ 验证计划的可行性",
        ]

        for i, thinking in enumerate(thinking_steps):
            await self._send_websocket_message(
                {
                    "type": "planning_thinking",
                    "data": {
                        "task_id": plan_id,
                        "session_id": session_id,
                        "message": thinking,
                        "stage": f"thinking_{i + 1}",
                        "progress": (i + 1) / len(thinking_steps) * 50,  # 思维过程占50%
                    },
                }
            )
            await asyncio.sleep(0.5)  # 模拟思考时间

        # 发送模型调用开始通知
        await self._send_websocket_message(
            {
                "type": "planning_thinking",
                "data": {
                    "task_id": plan_id,
                    "session_id": session_id,
                    "message": f"🧠 调用 {self.model_client.model} 生成执行计划...",
                    "stage": "model_generation",
                    "progress": 60,
                },
            }
        )

        # 使用模型生成计划
        raw_steps = await self.model_client.generate_plan(task_description)
        print(
            f"📋 [流式规划结果] 模型 {self.model_client.model} 生成了 {len(raw_steps)} 个步骤"
        )

        # 发送步骤生成进度
        await self._send_websocket_message(
            {
                "type": "planning_thinking",
                "data": {
                    "task_id": plan_id,
                    "session_id": session_id,
                    "message": f"📋 生成了 {len(raw_steps)} 个执行步骤",
                    "stage": "steps_generated",
                    "progress": 80,
                },
            }
        )

        # 转换为TaskStep对象，并实时发送每个步骤
        steps = []
        for i, step_data in enumerate(raw_steps):
            step = TaskStep(
                id=step_data.get("id", len(steps) + 1),
                action=step_data.get("action", "unknown"),
                tool=step_data.get("tool", "general"),
                parameters=step_data.get("parameters", {}),
                description=step_data.get("description", "无描述"),
            )
            steps.append(step)

            # 实时发送步骤信息
            await self._send_websocket_message(
                {
                    "type": "planning_step_added",
                    "data": {
                        "task_id": plan_id,
                        "session_id": session_id,
                        "step": {
                            "id": step.id,
                            "action": step.action,
                            "tool": step.tool,
                            "parameters": step.parameters,
                            "description": step.description,
                            "status": step.status.value,
                        },
                        "step_number": i + 1,
                        "total_steps": len(raw_steps),
                        "progress": 80 + (i + 1) / len(raw_steps) * 20,  # 剩余20%进度
                    },
                }
            )
            await asyncio.sleep(0.2)  # 短暂延迟以显示流式效果

        plan = TaskPlan(
            id=plan_id,
            description=task_description,
            steps=steps,
            created_at=asyncio.get_event_loop().time().__str__(),
        )

        self.plans[plan_id] = plan

        print(f"📋 已创建包含 {len(steps)} 个步骤的流式计划:")
        for i, step in enumerate(steps, 1):
            print(f"  {i}. {step.description} (使用 {step.tool})")

        return plan

    async def _send_websocket_message(self, message: dict):
        """发送WebSocket消息（如果有回调函数）"""
        if self.websocket_callback:
            try:
                await self.websocket_callback(message)
            except Exception as e:
                print(f"⚠️ WebSocket消息发送失败: {str(e)}")

    async def refine_plan(self, plan_id: str, feedback: str) -> TaskPlan:
        """Refine an existing plan based on feedback or new information"""

        if plan_id not in self.plans:
            raise ValueError(f"Plan {plan_id} not found")

        plan = self.plans[plan_id]

        # Use the model to refine the plan
        refine_prompt = f"""
        Original task: {plan.description}
        Current plan steps: {[step.description for step in plan.steps]}
        Feedback: {feedback}

        Please provide a refined plan as a JSON array of steps.
        """

        messages = [
            Message(
                role="system",
                content="You are refining a task execution plan based on feedback.",
            ),
            Message(role="user", content=refine_prompt),
        ]

        response_content = ""
        async for chunk in self.model_client.chat(messages):
            response_content += chunk

        # Update the plan (simplified for demo)
        print(f"🔄 Refined plan based on feedback: {feedback}")

        return plan

    def get_plan(self, plan_id: str) -> Optional[TaskPlan]:
        """Get a plan by ID"""
        return self.plans.get(plan_id)

    def list_plans(self) -> List[TaskPlan]:
        """List all plans"""
        return list(self.plans.values())

    def update_step_status(
        self,
        plan_id: str,
        step_id: int,
        status: TaskStatus,
        result: str = None,
        error: str = None,
    ):
        """Update the status of a specific step"""
        if plan_id in self.plans:
            plan = self.plans[plan_id]
            for step in plan.steps:
                if step.id == step_id:
                    step.status = status
                    if result:
                        step.result = result
                    if error:
                        step.error = error
                    break
