"""
MCP Server implementation using FastAPI and SSE
"""

import asyncio
import json
from typing import Dict, List, Optional
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse
from pydantic import BaseModel

from ..tools.manager import ToolManager


class MCPRequest(BaseModel):
    method: str
    params: Dict
    id: Optional[str] = None


class MCPResponse(BaseModel):
    result: Optional[Dict] = None
    error: Optional[str] = None
    id: Optional[str] = None


class MCPServer:
    """MCP Server for exposing tools via HTTP/SSE"""

    def __init__(self, tool_manager: ToolManager):
        self.tool_manager = tool_manager
        self.app = FastAPI(title="MCP Tool Server", version="1.0.0")
        self.active_connections: Dict[str, asyncio.Queue] = {}
        self._setup_routes()

    def _setup_routes(self):
        """Setup FastAPI routes"""

        @self.app.get("/")
        async def root():
            return {"message": "MCP Tool Server", "version": "1.0.0"}

        @self.app.get("/tools")
        async def list_tools():
            """List all available tools"""
            tools = self.tool_manager.list_tools()
            return {"tools": [tool.dict() for tool in tools]}

        @self.app.post("/execute")
        async def execute_tool(request: MCPRequest):
            """Execute a tool method"""
            try:
                method_parts = request.method.split(".")
                if len(method_parts) != 2:
                    raise ValueError("Method should be in format 'tool_name.action'")

                tool_name, action = method_parts
                result = await self.tool_manager.execute_tool(
                    tool_name, action, request.params
                )

                return MCPResponse(result={"output": result}, id=request.id)

            except Exception as e:
                return MCPResponse(error=str(e), id=request.id)

        @self.app.get("/stream/{client_id}")
        async def stream_events(client_id: str):
            """SSE endpoint for streaming tool execution events"""

            # Create a queue for this client
            if client_id not in self.active_connections:
                self.active_connections[client_id] = asyncio.Queue()

            async def event_generator():
                queue = self.active_connections[client_id]
                try:
                    while True:
                        # Wait for events
                        event = await queue.get()
                        yield {
                            "event": event.get("type", "message"),
                            "data": json.dumps(event.get("data", {})),
                        }
                except asyncio.CancelledError:
                    # Client disconnected
                    if client_id in self.active_connections:
                        del self.active_connections[client_id]
                    raise

            return EventSourceResponse(event_generator())

        @self.app.post("/stream/{client_id}/execute")
        async def stream_execute_tool(client_id: str, request: MCPRequest):
            """Execute tool and stream results via SSE"""

            if client_id not in self.active_connections:
                raise HTTPException(
                    status_code=404, detail="Client not connected to stream"
                )

            queue = self.active_connections[client_id]

            try:
                # Send start event
                await queue.put(
                    {
                        "type": "execution_start",
                        "data": {
                            "method": request.method,
                            "params": request.params,
                            "id": request.id,
                        },
                    }
                )

                # Execute tool
                method_parts = request.method.split(".")
                if len(method_parts) != 2:
                    raise ValueError("Method should be in format 'tool_name.action'")

                tool_name, action = method_parts
                result = await self.tool_manager.execute_tool(
                    tool_name, action, request.params
                )

                # Send result event
                await queue.put(
                    {
                        "type": "execution_result",
                        "data": {"result": result, "id": request.id},
                    }
                )

                return {"status": "started", "client_id": client_id}

            except Exception as e:
                # Send error event
                await queue.put(
                    {
                        "type": "execution_error",
                        "data": {"error": str(e), "id": request.id},
                    }
                )

                raise HTTPException(status_code=500, detail=str(e))

    async def broadcast_event(self, event: Dict):
        """Broadcast event to all connected clients"""
        for queue in self.active_connections.values():
            try:
                await queue.put(event)
            except:
                # Queue might be closed
                pass

    def get_app(self):
        """Get the FastAPI app instance"""
        return self.app
