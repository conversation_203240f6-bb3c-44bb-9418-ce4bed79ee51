#!/usr/bin/env python3
"""
演示脚本 - 启动Agent监控面板并打开浏览器
"""

import webbrowser
import time
import threading
import sys
from pathlib import Path


def open_browser_delayed():
    """延迟打开浏览器"""
    time.sleep(2)  # 等待服务器启动
    webbrowser.open("http://127.0.0.1:8001")
    print("🌐 浏览器已打开，访问地址: http://127.0.0.1:8001")


def main():
    """主函数"""
    print("🎬 启动Agent监控面板演示...")
    print("=" * 50)

    # 在后台线程中延迟打开浏览器
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()

    # 启动API服务器
    try:
        from api_server import main as start_api_server

        start_api_server()
    except KeyboardInterrupt:
        print("\n👋 演示结束，感谢使用！")
    except Exception as e:
        print(f"💥 启动失败: {e}")
        print("请确保已安装所有依赖：pip install -r requirements.txt")


if __name__ == "__main__":
    main()
