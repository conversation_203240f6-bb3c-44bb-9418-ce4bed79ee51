"""
Base classes for tools in the autonomous agent framework
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from pydantic import BaseModel


class ToolParameter(BaseModel):
    name: str
    type: str
    description: str
    required: bool = True
    default: Any = None


class ToolMetadata(BaseModel):
    name: str
    description: str
    parameters: List[ToolParameter]
    category: str = "general"


class BaseTool(ABC):
    """Base class for all tools"""

    def __init__(self):
        self.metadata = self.get_metadata()

    @abstractmethod
    def get_metadata(self) -> ToolMetadata:
        """Return metadata describing this tool"""
        pass

    @abstractmethod
    async def execute(self, action: str, parameters: Dict[str, Any]) -> Any:
        """Execute the tool with given action and parameters"""
        pass

    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """Validate that required parameters are provided"""
        missing_params = []
        for param in self.metadata.parameters:
            if param.required and param.name not in parameters:
                missing_params.append(param.name)

        if missing_params:
            available_params = list(parameters.keys())
            raise ValueError(
                f"工具 '{self.metadata.name}' 缺少必需参数: {missing_params}. "
                f"提供的参数: {available_params}. "
                f"期望的参数: {[p.name for p in self.metadata.parameters if p.required]}"
            )
        return True


class MCPTool(BaseTool):
    """Base class for MCP (Model Context Protocol) compatible tools"""

    def __init__(self, server_url: Optional[str] = None):
        super().__init__()
        self.server_url = server_url
        self.is_mcp_enabled = server_url is not None

    async def call_mcp_server(self, method: str, params: Dict) -> Any:
        """Call MCP server if available, otherwise use local implementation"""
        if self.is_mcp_enabled:
            # In a real implementation, this would make an MCP call
            # For demo purposes, we'll fall back to local execution
            pass

        return await self.execute_locally(method, params)

    @abstractmethod
    async def execute_locally(self, action: str, parameters: Dict[str, Any]) -> Any:
        """Local implementation when MCP server is not available"""
        pass

    async def execute(self, action: str, parameters: Dict[str, Any]) -> Any:
        """Execute via MCP if available, otherwise locally"""
        self.validate_parameters(parameters)

        if self.is_mcp_enabled:
            return await self.call_mcp_server(action, parameters)
        else:
            return await self.execute_locally(action, parameters)
