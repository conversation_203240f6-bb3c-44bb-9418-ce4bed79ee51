"""
Web Search Tool - Real web search functionality using DuckDuckGo
"""

import asyncio
from typing import Any, Dict, List
from urllib.parse import urlparse

from ..base import MCPTool, ToolMetadata, ToolParameter


class WebSearchTool(MCPTool):
    """Tool for real web search operations using DuckDuckGo"""

    def get_metadata(self) -> ToolMetadata:
        return ToolMetadata(
            name="web_search",
            description="Search the web for real information using DuckDuckGo",
            category="information",
            parameters=[
                ToolParameter(
                    name="query",
                    type="string",
                    description="Search query",
                    required=True,
                ),
                ToolParameter(
                    name="max_results",
                    type="integer",
                    description="Maximum number of results to return",
                    required=False,
                    default=5,
                ),
                ToolParameter(
                    name="region",
                    type="string",
                    description="Search region (e.g., 'cn-zh' for China, 'us-en' for US)",
                    required=False,
                    default="us-en",
                ),
                ToolParameter(
                    name="fetch_content",
                    type="boolean",
                    description="Whether to fetch content from top search results",
                    required=False,
                    default=True,
                ),
                ToolParameter(
                    name="max_pages",
                    type="integer",
                    description="Maximum number of pages to fetch content from",
                    required=False,
                    default=2,
                ),
            ],
        )

    async def execute_locally(self, action: str, parameters: Dict[str, Any]) -> Any:
        """Execute real web search using DuckDuckGo"""

        query = parameters.get("query")
        max_results = parameters.get("max_results", 5)
        region = parameters.get("region", "us-en")
        fetch_content = parameters.get("fetch_content", True)
        max_pages = parameters.get("max_pages", 2)

        try:
            # Perform real search
            results = await self._perform_real_search(query, max_results, region)

            # Format results for better display
            formatted_results = self._format_search_results(query, results)

            # 如果需要获取网页内容
            if fetch_content and results:
                content_results = await self._fetch_top_results_content(
                    results, max_pages
                )
                if content_results:
                    formatted_results += "\n\n" + "=" * 60 + "\n"
                    formatted_results += "🔍 **详细网页内容**:\n\n"
                    formatted_results += content_results

            return formatted_results

        except Exception as e:
            error_msg = f"搜索失败: {str(e)}"
            print(f"❌ {error_msg}")
            return error_msg

    async def _perform_real_search(
        self, query: str, max_results: int, region: str
    ) -> List[Dict]:
        """Perform real web search using DuckDuckGo"""

        try:
            from duckduckgo_search import DDGS

            # 在异步环境中运行同步的搜索
            def sync_search():
                with DDGS() as ddgs:
                    results = []
                    search_results = ddgs.text(
                        keywords=query,
                        region=region,
                        max_results=max_results,
                        safesearch="moderate",
                    )

                    for i, result in enumerate(search_results, 1):
                        # 提取域名
                        try:
                            from urllib.parse import urlparse

                            domain = urlparse(result.get("href", "")).netloc
                        except:
                            domain = "未知域名"

                        formatted_result = {
                            "rank": i,
                            "title": result.get("title", "无标题"),
                            "url": result.get("href", ""),
                            "snippet": result.get("body", "无摘要"),
                            "domain": domain,
                        }
                        results.append(formatted_result)

                    return results

            # 在线程池中运行同步搜索
            import concurrent.futures

            loop = asyncio.get_event_loop()

            with concurrent.futures.ThreadPoolExecutor() as executor:
                results = await loop.run_in_executor(executor, sync_search)

            return results

        except ImportError:
            raise Exception("DuckDuckGo搜索库未安装")
        except Exception as e:
            raise Exception(f"搜索过程中出错: {str(e)}")

    def _format_search_results(self, query: str, results: List[Dict]) -> str:
        """格式化搜索结果为易读的文本"""
        from rich.console import Console
        from rich.table import Table

        console = Console()

        # 创建搜索结果表格
        table = Table(
            title=f"🔍 搜索结果: {query}", show_header=True, header_style="bold blue"
        )
        table.add_column("排名", style="cyan", width=4)
        table.add_column("标题", style="white", width=40)
        table.add_column("来源", style="yellow", width=20)
        table.add_column("摘要", style="green", width=50)

        for result in results:
            rank = str(result.get("rank", ""))
            title = (
                result.get("title", "无标题")[:35] + "..."
                if len(result.get("title", "")) > 35
                else result.get("title", "无标题")
            )
            domain = result.get("domain", "未知来源")
            snippet = (
                result.get("snippet", "无摘要")[:45] + "..."
                if len(result.get("snippet", "")) > 45
                else result.get("snippet", "无摘要")
            )

            table.add_row(rank, title, domain, snippet)

        # 使用 console.capture() 来获取表格的字符串表示
        with console.capture() as capture:
            console.print(table)

        # 创建简洁的文本总结
        summary = f"搜索查询: {query}\n"
        summary += f"找到 {len(results)} 个相关结果:\n\n"

        for i, result in enumerate(results, 1):
            summary += f"{i}. {result.get('title', '无标题')}\n"
            summary += f"   来源: {result.get('domain', '未知')}\n"
            summary += f"   摘要: {result.get('snippet', '无摘要')}\n"
            summary += f"   链接: {result.get('url', '无链接')}\n\n"

        # 在控制台显示表格
        console.print(table)

        return summary

    async def get_page_content(self, url: str) -> str:
        """获取网页内容"""
        try:
            import httpx
            from bs4 import BeautifulSoup

            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(
                    url,
                    headers={
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                    },
                )
                response.raise_for_status()

                # 解析HTML内容
                soup = BeautifulSoup(response.text, "html.parser")

                # 移除脚本和样式标签
                for script in soup(["script", "style"]):
                    script.decompose()

                # 提取主要内容
                content_selectors = [
                    "article",
                    "main",
                    ".content",
                    ".article-content",
                    ".post-content",
                    ".entry-content",
                    "#content",
                ]

                main_content = None
                for selector in content_selectors:
                    main_content = soup.select_one(selector)
                    if main_content:
                        break

                if not main_content:
                    main_content = soup.find("body")

                if main_content:
                    # 提取文本内容
                    text = main_content.get_text(separator="\n", strip=True)
                    # 清理多余的空行
                    lines = [line.strip() for line in text.split("\n") if line.strip()]
                    content = "\n".join(lines)

                    # 增加内容长度限制，允许更多内容显示
                    if len(content) > 8000:
                        content = (
                            content[:8000]
                            + "...\n[内容已截断，如需完整内容请联系管理员]"
                        )

                    return content
                else:
                    return "无法提取网页内容"

        except ImportError:
            return "需要安装 httpx 和 beautifulsoup4 库来获取网页内容"
        except Exception as e:
            return f"获取网页内容失败: {str(e)}"

    async def _fetch_top_results_content(
        self, results: List[Dict], max_pages: int
    ) -> str:
        """获取搜索结果中前几个网页的内容"""
        content_parts = []

        for i, result in enumerate(results[:max_pages]):
            url = result.get("url", "")
            title = result.get("title", "无标题")

            if not url:
                continue

            print(f"🔍 正在获取网页内容: {title}")

            try:
                content = await self.get_page_content(url)
                if content and len(content.strip()) > 100:  # 只保留有意义的内容
                    content_parts.append(f"**网页 {i + 1}: {title}**\n")
                    content_parts.append(f"链接: {url}\n")
                    content_parts.append(f"{content}\n")
                    content_parts.append("-" * 50 + "\n")
                else:
                    content_parts.append(
                        f"**网页 {i + 1}: {title}** - 无法获取有效内容\n"
                    )

            except Exception as e:
                content_parts.append(
                    f"**网页 {i + 1}: {title}** - 获取失败: {str(e)}\n"
                )

        return "\n".join(content_parts) if content_parts else "未能获取到网页内容"
