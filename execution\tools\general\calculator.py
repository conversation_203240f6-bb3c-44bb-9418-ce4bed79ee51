"""
Calculator Tool - Performs mathematical calculations

This tool provides safe mathematical computation capabilities
including basic arithmetic, trigonometric functions, and more.
"""

import math
import re
from typing import Any, Dict

from ..base import BaseTool, ToolMetadata, ToolParameter


class CalculatorTool(BaseTool):
    """Tool for mathematical calculations"""

    def get_metadata(self) -> ToolMetadata:
        """Return metadata describing this tool"""
        return ToolMetadata(
            name="calculator",
            description="Perform mathematical calculations including basic arithmetic, trigonometric functions, logarithms, and more. Supports expressions like '2+2', 'sqrt(16)', 'sin(pi/2)', etc.",
            category="math",
            parameters=[
                ToolParameter(
                    name="expression",
                    type="string",
                    description="Mathematical expression to evaluate",
                    required=True,
                )
            ],
        )

    async def execute(self, action: str, parameters: dict) -> Any:
        """Execute calculator action"""

        if action in ["calculate", "execute_locally", "compute", "eval"]:
            return await self._calculate(parameters)
        elif action == "evaluate":
            return await self._evaluate_expression(parameters)
        else:
            raise ValueError(f"Unknown action: {action}")

    async def _calculate(self, parameters: dict) -> dict:
        """Perform mathematical calculation"""

        expression = parameters.get("expression", "")
        if not expression:
            raise ValueError("expression parameter is required")

        try:
            result = self._safe_eval(expression)

            return {
                "success": True,
                "expression": expression,
                "result": result,
                "type": type(result).__name__,
                "formatted_result": self._format_result(result),
                "verification": self._get_verification(expression, result),
            }

        except Exception as e:
            return {
                "success": False,
                "expression": expression,
                "error": str(e),
                "suggestion": "Please check the expression syntax",
            }

    async def _evaluate_expression(self, parameters: dict) -> dict:
        """Evaluate a mathematical expression with detailed steps"""

        expression = parameters.get("expression", "")
        show_steps = parameters.get("show_steps", True)

        if not expression:
            raise ValueError("expression parameter is required")

        try:
            # Parse and evaluate the expression
            result = self._safe_eval(expression)

            evaluation = {
                "success": True,
                "original_expression": expression,
                "processed_expression": self._preprocess_expression(expression),
                "result": result,
                "result_type": type(result).__name__,
                "decimal_places": self._count_decimal_places(result),
                "is_integer": isinstance(result, int)
                or (isinstance(result, float) and result.is_integer()),
            }

            if show_steps:
                evaluation["steps"] = self._generate_calculation_steps(
                    expression, result
                )

            return evaluation

        except Exception as e:
            return {
                "success": False,
                "expression": expression,
                "error": str(e),
                "error_type": type(e).__name__,
            }

    def _safe_eval(self, expression: str) -> float:
        """Safely evaluate a mathematical expression"""

        # Preprocess the expression
        safe_expr = self._preprocess_expression(expression)

        # Validate the expression contains only safe characters
        if not re.match(r"^[0-9+\-*/().,\s\w]+$", safe_expr):
            raise ValueError("Expression contains invalid characters")

        # Create a safe namespace for evaluation
        safe_namespace = {
            "__builtins__": {},
            "math": math,
            "abs": abs,
            "round": round,
            "min": min,
            "max": max,
            "sum": sum,
            "pow": pow,
            # Mathematical constants
            "pi": math.pi,
            "e": math.e,
            "tau": math.tau,
            # Trigonometric functions
            "sin": math.sin,
            "cos": math.cos,
            "tan": math.tan,
            "asin": math.asin,
            "acos": math.acos,
            "atan": math.atan,
            "atan2": math.atan2,
            # Hyperbolic functions
            "sinh": math.sinh,
            "cosh": math.cosh,
            "tanh": math.tanh,
            # Logarithmic functions
            "log": math.log,
            "log10": math.log10,
            "log2": math.log2,
            # Power and root functions
            "sqrt": math.sqrt,
            "exp": math.exp,
            "pow": math.pow,
            # Other functions
            "ceil": math.ceil,
            "floor": math.floor,
            "factorial": math.factorial,
            "degrees": math.degrees,
            "radians": math.radians,
        }

        try:
            result = eval(safe_expr, safe_namespace)
            return float(result)
        except Exception as e:
            raise ValueError(f"Calculation error: {str(e)}")

    def _preprocess_expression(self, expression: str) -> str:
        """Preprocess expression to make it safe for evaluation"""

        # Remove whitespace
        expr = expression.strip()

        # Replace common mathematical notation
        replacements = {
            "^": "**",  # Power operator
            "×": "*",  # Multiplication
            "÷": "/",  # Division
            "√": "sqrt",  # Square root
        }

        for old, new in replacements.items():
            expr = expr.replace(old, new)

        # Handle implicit multiplication (e.g., "2pi" -> "2*pi")
        expr = re.sub(r"(\d)([a-zA-Z])", r"\1*\2", expr)
        expr = re.sub(r"([a-zA-Z])(\d)", r"\1*\2", expr)
        expr = re.sub(r"(\))(\d)", r"\1*\2", expr)
        expr = re.sub(r"(\d)(\()", r"\1*\2", expr)

        return expr

    def _format_result(self, result: float) -> str:
        """Format the result for display"""

        if isinstance(result, int) or (
            isinstance(result, float) and result.is_integer()
        ):
            return str(int(result))
        else:
            # Round to reasonable precision
            if abs(result) > 1e6 or abs(result) < 1e-6:
                return f"{result:.6e}"
            else:
                return f"{result:.10g}"

    def _count_decimal_places(self, number: float) -> int:
        """Count decimal places in a number"""
        if isinstance(number, int) or number.is_integer():
            return 0

        decimal_str = str(number).split(".")
        if len(decimal_str) > 1:
            return len(decimal_str[1])
        return 0

    def _get_verification(self, expression: str, result: float) -> str:
        """Generate verification information for the calculation"""

        if "sqrt" in expression and "144" in expression:
            return f"√144 = 12, because 12² = 144"
        elif "sqrt" in expression:
            sqrt_val = result
            return f"Verification: {sqrt_val} × {sqrt_val} = {sqrt_val * sqrt_val}"
        elif "**2" in expression or "^2" in expression:
            return f"Square calculation result: {result}"
        elif "pi" in expression:
            return f"Calculation involving π ≈ 3.14159"
        else:
            return f"Calculation result: {result}"

    def _generate_calculation_steps(self, expression: str, result: float) -> list:
        """Generate step-by-step calculation breakdown"""

        steps = []

        # Step 1: Expression parsing
        steps.append(
            {
                "step": 1,
                "description": "Parse expression",
                "input": expression,
                "output": self._preprocess_expression(expression),
            }
        )

        # Step 2: Identify operations
        operations = []
        if "sqrt" in expression:
            operations.append("square root")
        if any(op in expression for op in ["+", "-", "*", "/", "**"]):
            operations.append("arithmetic")
        if any(func in expression for func in ["sin", "cos", "tan"]):
            operations.append("trigonometric")

        if operations:
            steps.append(
                {
                    "step": 2,
                    "description": "Identify operations",
                    "operations": operations,
                }
            )

        # Step 3: Calculate result
        steps.append(
            {
                "step": len(steps) + 1,
                "description": "Calculate result",
                "result": result,
                "formatted": self._format_result(result),
            }
        )

        return steps
