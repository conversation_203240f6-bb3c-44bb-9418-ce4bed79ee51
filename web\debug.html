<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript 调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .debug-button:hover {
            background: #0056b3;
        }
        .debug-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.connected { background: #28a745; }
        .status-indicator.connecting { background: #ffc107; }
        .status-indicator.disconnected { background: #dc3545; }
        .status-indicator.error { background: #dc3545; }
    </style>
</head>
<body>
    <h1>JavaScript 模块调试页面</h1>
    
    <div class="debug-panel">
        <h3>模块加载状态</h3>
        <div id="module-status"></div>
    </div>
    
    <div class="debug-panel">
        <h3>WebSocket 连接状态</h3>
        <div id="websocket-status"></div>
        <button class="debug-button" onclick="testWebSocket()">测试WebSocket连接</button>
    </div>
    
    <div class="debug-panel">
        <h3>配置信息</h3>
        <div id="config-info"></div>
        <button class="debug-button" onclick="testConfig()">获取配置信息</button>
    </div>
    
    <div class="debug-panel">
        <h3>会话管理</h3>
        <div id="session-info"></div>
        <button class="debug-button" onclick="testSession()">测试会话管理</button>
    </div>
    
    <div class="debug-panel">
        <h3>消息渲染</h3>
        <div id="message-info"></div>
        <button class="debug-button" onclick="testMessageRender()">测试消息渲染</button>
    </div>
    
    <div class="debug-panel">
        <h3>事件处理</h3>
        <div id="event-info"></div>
        <button class="debug-button" onclick="testEvents()">测试事件处理</button>
    </div>
    
    <div class="debug-panel">
        <h3>调试输出</h3>
        <div id="debug-output" class="debug-output"></div>
        <button class="debug-button" onclick="clearOutput()">清空输出</button>
    </div>

    <!-- 加载模块 -->
    <script src="js/config.js?v=20250129-6"></script>
    <script src="js/websocket.js?v=20250129-6"></script>
    <script src="js/session.js?v=20250129-6"></script>
    <script src="js/message-renderer.js?v=20250129-6"></script>
    <script src="js/event-handler.js?v=20250129-6"></script>
    <script src="js/message-handler.js?v=20250129-6"></script>
    <script src="js/agent-chat.js?v=20250129-6"></script>

    <script>
        let agentChat = null;
        
        function log(message) {
            const output = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            document.getElementById('debug-output').textContent = '';
        }
        
        function checkModules() {
            const requiredClasses = [
                'ConfigManager',
                'SessionManager', 
                'MessageRenderer',
                'MessageHandler',
                'EventHandler',
                'WebSocketManager',
                'AgentChat'
            ];
            
            const statusDiv = document.getElementById('module-status');
            let html = '';
            
            requiredClasses.forEach(className => {
                const exists = window[className] !== undefined;
                const status = exists ? '✅' : '❌';
                html += `<div>${status} ${className}</div>`;
            });
            
            statusDiv.innerHTML = html;
            
            const allLoaded = requiredClasses.every(className => window[className]);
            log(`模块检查完成: ${allLoaded ? '所有模块已加载' : '部分模块缺失'}`);
            
            return allLoaded;
        }
        
        function testWebSocket() {
            if (!agentChat) {
                log('AgentChat 实例未创建');
                return;
            }
            
            const state = agentChat.websocketManager.getConnectionState();
            const isConnected = agentChat.websocketManager.isConnected();
            
            const statusDiv = document.getElementById('websocket-status');
            statusDiv.innerHTML = `
                <div><span class="status-indicator ${state}"></span>连接状态: ${state}</div>
                <div>是否已连接: ${isConnected ? '是' : '否'}</div>
            `;
            
            log(`WebSocket状态: ${state}, 已连接: ${isConnected}`);
        }
        
        function testConfig() {
            if (!agentChat) {
                log('AgentChat 实例未创建');
                return;
            }
            
            const config = agentChat.configManager;
            const statusDiv = document.getElementById('config-info');
            statusDiv.innerHTML = `
                <div>当前模型: ${config.currentModel}</div>
                <div>调试模式: ${config.debugMode ? '开启' : '关闭'}</div>
                <div>COT模型数量: ${config.cotModels.length}</div>
                <div>可用模型数量: ${config.availableModels.length}</div>
            `;
            
            log(`配置信息: 模型=${config.currentModel}, 调试=${config.debugMode}`);
        }
        
        function testSession() {
            if (!agentChat) {
                log('AgentChat 实例未创建');
                return;
            }
            
            const session = agentChat.sessionManager.getCurrentSession();
            const statusDiv = document.getElementById('session-info');
            
            if (session) {
                statusDiv.innerHTML = `
                    <div>会话ID: ${session.id}</div>
                    <div>会话标题: ${session.title}</div>
                    <div>消息数量: ${session.messages.length}</div>
                    <div>创建时间: ${session.createdAt.toLocaleString()}</div>
                `;
                log(`当前会话: ${session.title}, 消息数: ${session.messages.length}`);
            } else {
                statusDiv.innerHTML = '<div>无当前会话</div>';
                log('无当前会话');
            }
        }
        
        function testMessageRender() {
            if (!agentChat) {
                log('AgentChat 实例未创建');
                return;
            }
            
            // 创建测试消息
            const testMessage = {
                id: Date.now(),
                role: 'user',
                content: '这是一条测试消息',
                timestamp: new Date()
            };
            
            try {
                const html = agentChat.messageRenderer.renderMessage(testMessage);
                const statusDiv = document.getElementById('message-info');
                statusDiv.innerHTML = `
                    <div>消息渲染测试: ✅ 成功</div>
                    <div>HTML长度: ${html.length} 字符</div>
                `;
                log('消息渲染测试成功');
            } catch (error) {
                const statusDiv = document.getElementById('message-info');
                statusDiv.innerHTML = `<div>消息渲染测试: ❌ 失败 - ${error.message}</div>`;
                log(`消息渲染测试失败: ${error.message}`);
            }
        }
        
        function testEvents() {
            if (!agentChat) {
                log('AgentChat 实例未创建');
                return;
            }
            
            const statusDiv = document.getElementById('event-info');
            statusDiv.innerHTML = `
                <div>事件处理器: ✅ 已初始化</div>
                <div>处理状态: ${agentChat.eventHandler.isProcessing ? '处理中' : '空闲'}</div>
            `;
            
            log('事件处理器状态正常');
        }
        
        async function initializeDebug() {
            log('开始调试初始化...');
            
            // 检查模块
            if (!checkModules()) {
                log('模块检查失败，停止初始化');
                return;
            }
            
            try {
                // 创建AgentChat实例
                agentChat = new AgentChat();
                window.debugAgentChat = agentChat;
                
                log('AgentChat 实例创建成功');
                
                // 等待初始化完成
                setTimeout(() => {
                    testWebSocket();
                    testConfig();
                    testSession();
                }, 2000);
                
            } catch (error) {
                log(`AgentChat 初始化失败: ${error.message}`);
            }
        }
        
        // 页面加载完成后开始调试
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，开始调试...');
            setTimeout(initializeDebug, 500);
        });
        
        // 全局错误处理
        window.addEventListener('error', (event) => {
            log(`全局错误: ${event.error.message}`);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            log(`未处理的Promise拒绝: ${event.reason}`);
        });
    </script>
</body>
</html>
