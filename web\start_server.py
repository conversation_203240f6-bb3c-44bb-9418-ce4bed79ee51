#!/usr/bin/env python3
"""
启动Agent监控面板服务器的脚本
"""

import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import fastapi
        import uvicorn
        import websockets
        import pydantic
        import httpx
        import rich
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        return False

def install_dependencies():
    """安装依赖"""
    requirements_file = Path(__file__).parent / "requirements.txt"
    if requirements_file.exists():
        print("📦 正在安装依赖...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ])
            print("✅ 依赖安装完成")
            return True
        except subprocess.CalledProcessError:
            print("❌ 依赖安装失败")
            return False
    else:
        print("❌ 找不到requirements.txt文件")
        return False

def main():
    """主函数"""
    print("🚀 启动Agent监控面板...")
    
    # 检查依赖
    if not check_dependencies():
        print("🔧 尝试安装缺少的依赖...")
        if not install_dependencies():
            print("💥 无法安装依赖，请手动安装")
            return
        
        # 重新检查
        if not check_dependencies():
            print("💥 依赖安装后仍然无法导入，请检查环境")
            return
    
    # 启动服务器
    try:
        from api_server import main as start_api_server
        start_api_server()
    except Exception as e:
        print(f"💥 启动服务器失败: {e}")
        print("请确保Ollama服务正在运行，并且配置正确")

if __name__ == "__main__":
    main()
