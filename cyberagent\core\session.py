"""
Session Management - Handles conversation context and history
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from pydantic import BaseModel


class ConversationMessage(BaseModel):
    """单条对话消息"""
    id: str
    timestamp: datetime
    role: str  # 'user' or 'assistant'
    content: str
    task_id: Optional[str] = None
    metadata: Dict[str, Any] = {}


class TaskContext(BaseModel):
    """任务上下文信息"""
    task_id: str
    description: str
    status: str
    created_at: datetime
    completed_at: Optional[datetime] = None
    plan_steps: List[Dict] = []
    results: List[Dict] = []
    related_tasks: List[str] = []


class SessionState(BaseModel):
    """会话状态"""
    session_id: str
    created_at: datetime
    last_activity: datetime
    messages: List[ConversationMessage] = []
    tasks: List[TaskContext] = []
    context_variables: Dict[str, Any] = {}
    user_preferences: Dict[str, Any] = {}


class SessionManager:
    """会话管理器"""
    
    def __init__(self, session_dir: Optional[str] = None):
        self.session_dir = Path(session_dir or Path.home() / ".agent_sessions")
        self.session_dir.mkdir(exist_ok=True)
        
        self.current_session: Optional[SessionState] = None
        self.max_context_messages = 20  # 最大上下文消息数
        self.max_session_age_days = 30  # 会话最大保存天数
    
    def create_session(self) -> SessionState:
        """创建新会话"""
        session_id = str(uuid.uuid4())
        now = datetime.now()
        
        session = SessionState(
            session_id=session_id,
            created_at=now,
            last_activity=now
        )
        
        self.current_session = session
        self._save_session(session)
        return session
    
    def load_session(self, session_id: str) -> Optional[SessionState]:
        """加载指定会话"""
        session_file = self.session_dir / f"{session_id}.json"
        
        if not session_file.exists():
            return None
        
        try:
            with open(session_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            session = SessionState(**data)
            self.current_session = session
            return session
            
        except Exception as e:
            print(f"⚠️ 加载会话失败: {e}")
            return None
    
    def get_current_session(self) -> SessionState:
        """获取当前会话，如果不存在则创建新会话"""
        if self.current_session is None:
            return self.create_session()
        return self.current_session
    
    def add_message(self, role: str, content: str, task_id: Optional[str] = None, metadata: Dict = None) -> ConversationMessage:
        """添加消息到当前会话"""
        session = self.get_current_session()
        
        message = ConversationMessage(
            id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            role=role,
            content=content,
            task_id=task_id,
            metadata=metadata or {}
        )
        
        session.messages.append(message)
        session.last_activity = datetime.now()
        
        # 限制消息数量
        if len(session.messages) > self.max_context_messages:
            session.messages = session.messages[-self.max_context_messages:]
        
        self._save_session(session)
        return message
    
    def add_task_context(self, task_id: str, description: str, plan_steps: List[Dict] = None) -> TaskContext:
        """添加任务上下文"""
        session = self.get_current_session()
        
        task_context = TaskContext(
            task_id=task_id,
            description=description,
            status="created",
            created_at=datetime.now(),
            plan_steps=plan_steps or []
        )
        
        session.tasks.append(task_context)
        session.last_activity = datetime.now()
        
        self._save_session(session)
        return task_context
    
    def update_task_status(self, task_id: str, status: str, results: List[Dict] = None):
        """更新任务状态"""
        session = self.get_current_session()
        
        for task in session.tasks:
            if task.task_id == task_id:
                task.status = status
                if status == "completed":
                    task.completed_at = datetime.now()
                if results:
                    task.results = results
                break
        
        session.last_activity = datetime.now()
        self._save_session(session)
    
    def get_recent_context(self, max_messages: int = 10) -> List[ConversationMessage]:
        """获取最近的对话上下文"""
        session = self.get_current_session()
        return session.messages[-max_messages:] if session.messages else []
    
    def get_related_tasks(self, current_task_description: str, max_tasks: int = 3) -> List[TaskContext]:
        """获取相关的历史任务"""
        session = self.get_current_session()
        
        # 简单的关键词匹配来找相关任务
        related_tasks = []
        current_keywords = set(current_task_description.lower().split())
        
        for task in session.tasks:
            task_keywords = set(task.description.lower().split())
            # 计算关键词重叠度
            overlap = len(current_keywords & task_keywords)
            if overlap > 0:
                related_tasks.append((task, overlap))
        
        # 按相关度排序
        related_tasks.sort(key=lambda x: x[1], reverse=True)
        return [task for task, _ in related_tasks[:max_tasks]]
    
    def set_context_variable(self, key: str, value: Any):
        """设置上下文变量"""
        session = self.get_current_session()
        session.context_variables[key] = value
        session.last_activity = datetime.now()
        self._save_session(session)
    
    def get_context_variable(self, key: str, default: Any = None) -> Any:
        """获取上下文变量"""
        session = self.get_current_session()
        return session.context_variables.get(key, default)
    
    def clear_session(self):
        """清空当前会话"""
        if self.current_session:
            session_file = self.session_dir / f"{self.current_session.session_id}.json"
            if session_file.exists():
                session_file.unlink()
        self.current_session = None
    
    def list_sessions(self) -> List[Dict[str, Any]]:
        """列出所有会话"""
        sessions = []
        
        for session_file in self.session_dir.glob("*.json"):
            try:
                with open(session_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                sessions.append({
                    "session_id": data["session_id"],
                    "created_at": data["created_at"],
                    "last_activity": data["last_activity"],
                    "message_count": len(data.get("messages", [])),
                    "task_count": len(data.get("tasks", []))
                })
                
            except Exception:
                continue
        
        # 按最后活动时间排序
        sessions.sort(key=lambda x: x["last_activity"], reverse=True)
        return sessions
    
    def _save_session(self, session: SessionState):
        """保存会话到文件"""
        session_file = self.session_dir / f"{session.session_id}.json"
        
        try:
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session.model_dump(), f, indent=2, ensure_ascii=False, default=str)
        except Exception as e:
            print(f"⚠️ 保存会话失败: {e}")
    
    def cleanup_old_sessions(self):
        """清理过期会话"""
        cutoff_date = datetime.now().timestamp() - (self.max_session_age_days * 24 * 3600)
        
        for session_file in self.session_dir.glob("*.json"):
            if session_file.stat().st_mtime < cutoff_date:
                try:
                    session_file.unlink()
                    print(f"🗑️ 已清理过期会话: {session_file.name}")
                except Exception as e:
                    print(f"⚠️ 清理会话失败: {e}")


# 全局会话管理器实例
_session_manager = None


def get_session_manager() -> SessionManager:
    """获取全局会话管理器实例"""
    global _session_manager
    if _session_manager is None:
        _session_manager = SessionManager()
    return _session_manager
