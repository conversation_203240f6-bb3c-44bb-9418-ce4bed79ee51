# CyberAgent - 智能网络安全分析框架

[![Python](https://img.shields.io/badge/Python-3.10+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![AutoGen Compatible](https://img.shields.io/badge/AutoGen-Compatible-orange.svg)](https://github.com/microsoft/autogen)
[![Security](https://img.shields.io/badge/Focus-Cybersecurity-red.svg)](https://github.com/jumpjumpb3ar/CyberAgent)

**CyberAgent** 是一个专门为威胁检测、事件响应和安全研究设计的自主网络安全分析框架。基于 Microsoft AutoGen 兼容性构建，提供智能任务分解、安全工具编排和自动化威胁分析能力。

## ✨ 核心特性

- 🛡️ **安全优先设计** - 专为网络安全分析和威胁检测而构建
- 🔍 **威胁情报分析** - 自动化 IOC 分析、信誉检查和威胁归因
- 📊 **日志分析** - 高级安全日志解析和异常检测
- 🤖 **自主调查** - 自主威胁狩猎和事件分析
- 🔧 **安全工具集成** - 通过 MCP 协议无缝集成安全工具
- 🌐 **多界面支持** - 终端、Web UI 和 MCP 服务器模式
- 🔗 **AutoGen 兼容** - 可与 Microsoft AutoGen 框架集成
- 💾 **上下文感知分析** - 持久化调查上下文和证据关联
- 🚀 **实时监控** - 实时威胁检测和响应能力

## 🚀 快速开始

### 安装

```bash
# 克隆仓库
git clone https://github.com/jumpjumpb3ar/CyberAgent.git
cd CyberAgent

# 使用 uv 安装依赖（推荐）
uv sync

# 或使用 pip
pip install -r requirements.txt
```

### 基本使用

```bash
# 快速启动演示（推荐新手）
python quick_start.py

# 启动交互式终端模式
python main.py

# 启动 Web 界面
python main.py --mode web

# 使用指定模型启动
python main.py --model qwen3:32b

# 启动 MCP 服务器
python main.py --mode mcp
```

### 🎯 演示任务

启动后，您可以尝试以下任务：

- **数学计算**: "计算半径为 10 的圆的面积"
- **信息搜索**: "搜索 Python 学习教程"
- **安全分析**: "分析 IP 地址 ************ 的威胁情况"
- **文件操作**: "创建一个安全事件报告模板"

## 🏗️ 系统架构

CyberAgent 采用安全优先的模块化架构，专为网络安全分析和 AutoGen 兼容性设计：

```
CyberAgent/
├── main.py                    # 统一入口点
├── quick_start.py            # 快速启动脚本
├── demo.py                   # 演示启动脚本
├── cyberagent/               # 核心框架
│   ├── core/                 # 核心组件
│   │   ├── agent.py          # 主要 CyberAgent 类
│   │   ├── api.py            # AutoGen 兼容 API 接口
│   │   ├── model.py          # LLM 客户端 (Ollama/OpenAI)
│   │   ├── planner.py        # 安全任务规划引擎
│   │   ├── executor.py       # 执行引擎
│   │   ├── config.py         # 配置管理
│   │   └── session.py        # 调查上下文管理
│   └── interfaces/           # 接口层
│       ├── terminal.py       # 终端接口
│       ├── api_server.py     # Web API 服务器
│       └── mcp_server.py     # MCP 服务器
├── execution/                # 执行层
│   ├── tools/               # 工具生态系统
│   │   ├── base.py          # 工具基类
│   │   ├── manager.py       # 工具注册和管理
│   │   ├── security/        # 安全专用工具
│   │   │   ├── log_analyzer.py      # 安全日志分析
│   │   │   ├── threat_intel.py     # 威胁情报查询
│   │   │   ├── file_scanner.py     # 文件分析工具
│   │   │   └── network_probe.py    # 网络侦察
│   │   └── general/         # 通用工具
│   │       ├── file_ops.py  # 文件系统操作
│   │       ├── web_search.py # Web 搜索功能
│   │       ├── web_fetch.py # Web 内容检索
│   │       └── calculator.py # 数学计算工具
│   └── mcp/                 # 模型上下文协议
│       ├── server.py        # MCP 服务器实现
│       └── client.py        # MCP 客户端
├── web/                     # Web 界面
│   ├── index.html          # 主页面
│   ├── styles.css          # 样式文件
│   ├── script.js           # JavaScript 脚本
│   └── ...
└── docs/                   # 文档
```

## 🔧 使用示例

### 快速启动模式

```bash
# 最简单的启动方式
python quick_start.py
```

### 终端模式

```python
from cyberagent.core.agent import CyberAgent
from cyberagent.core.api import TaskRequest

# 初始化智能体
agent = CyberAgent()

# 处理任务
request = TaskRequest(task="计算半径为 5 的圆的面积")
result = await agent.process_task(request)

print(f"结果: {result.summary}")
```

### Web 界面

启动 Web 服务器并打开浏览器：

```bash
python main.py --mode web --port 8001
# 打开 http://localhost:8001
```

### AutoGen 集成

CyberAgent 提供 AutoGen 兼容接口：

```python
from cyberagent.core.api import ConversationRequest, AgentMessage, MessageRole

# 创建 AutoGen 风格的对话
messages = [
    AgentMessage(role=MessageRole.USER, content="你好，你能帮助我吗？"),
    AgentMessage(role=MessageRole.ASSISTANT, content="当然可以！您需要什么帮助？")
]

request = ConversationRequest(messages=messages, max_turns=5)
response = await agent.start_conversation(request)
```

## 🛠️ 可用工具

| 工具 | 描述 | 使用示例 |
|------|-------------|-------|
| **计算器** | 数学计算功能 | `"计算 2 + 2 * 3"` |
| **文件操作** | 文件系统管理 | `"创建一个名为 test.txt 的文件"` |
| **网络搜索** | 互联网搜索功能 | `"搜索 Python 教程"` |
| **网页获取** | 网页内容检索 | 与搜索结果自动配合使用 |

## ⚙️ 配置

通过环境变量或配置文件配置 CyberAgent：

```bash
# 模型设置
export AGENT_MODEL_NAME="qwen3:32b"
export AGENT_MODEL_BASE_URL="http://********:11434"

# 显示设置
export AGENT_SHOW_THINKING="true"
export AGENT_DETAILED_LOGS="true"
```

或创建 `config.json` 文件：

```json
{
  "model_name": "qwen3:32b",
  "model_base_url": "http://********:11434",
  "show_thinking_process": true,
  "max_retries": 3,
  "timeout_seconds": 300
}
```

## 🔌 扩展 CyberAgent

### 添加自定义工具

```python
from execution.tools.base import BaseTool, ToolMetadata

class MyCustomTool(BaseTool):
    def get_metadata(self):
        return ToolMetadata(
            name="my_tool",
            description="我的自定义工具",
            category="custom"
        )

    async def execute(self, action: str, parameters: dict):
        # 您的工具逻辑代码
        return {"result": "success"}

# 注册工具
tool_manager.register_tool(MyCustomTool())
```

### 自定义模型客户端

```python
from cyberagent.core.model import OllamaClient

class MyModelClient(OllamaClient):
    async def chat(self, messages, stream=False):
        # 自定义模型集成
        pass
```

## 🤝 AutoGen 集成

CyberAgent 专为与 Microsoft AutoGen 无缝集成而设计：

- **兼容消息格式** - 使用 AutoGen 的消息结构
- **智能体配置** - 支持 AutoGen 智能体配置
- **对话模式** - 多轮对话和群组聊天
- **工具调用** - 与 AutoGen 兼容的函数调用
- **可扩展架构** - 易于集成 AutoGen 组件

## 📚 文档

- [框架分析](docs/FRAMEWORK_ANALYSIS.md) - 详细的架构概述
- [API 参考](docs/api-reference.md) - 完整的 API 文档
- [工具开发](docs/tool-development.md) - 创建自定义工具指南
- [AutoGen 集成](docs/autogen-integration.md) - AutoGen 兼容性指南

## 🤝 贡献

我们欢迎贡献！请查看我们的[贡献指南](CONTRIBUTING.md)了解详情。

1. Fork 仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交您的更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 详情请查看 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- [Microsoft AutoGen](https://github.com/microsoft/autogen) - 提供了启发性的多智能体框架
- [Ollama](https://ollama.ai/) - 提供了本地 LLM 推理能力
- [FastAPI](https://fastapi.tiangolo.com/) - 提供了优秀的 Web 框架
- [Rich](https://github.com/Textualize/rich) - 提供了美观的终端界面

## 📞 支持

- 📧 邮箱: <EMAIL>
- 💬 GitHub Issues: [创建问题](https://github.com/jumpjumpb3ar/AutoAgent/issues)
- 📖 文档: [阅读文档](docs/)

---

**CyberAgent** - 构建自主 AI 智能体的未来，一次一个任务。🚀