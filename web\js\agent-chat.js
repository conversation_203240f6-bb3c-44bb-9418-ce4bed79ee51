// 主要的AgentChat类 - 整合所有模块
class AgentChat {
    constructor() {
        // 初始化各个管理器
        this.configManager = new ConfigManager();
        this.sessionManager = new SessionManager();
        this.messageRenderer = new MessageRenderer(this.configManager);
        this.messageHandler = new MessageHandler(this);
        this.eventHandler = new EventHandler(this);
        this.websocketManager = new WebSocketManager((message) => this.messageHandler.handleMessage(message));

        // 初始化应用
        this.initialize();
    }

    // 初始化应用
    async initialize() {
        try {
            // 加载配置
            await this.configManager.loadConfig();

            // 加载可用模型
            const models = await this.configManager.loadAvailableModels();
            this.updateModelSelectors(models);

            // 初始化WebSocket
            this.websocketManager.initialize();

            // WebSocket连接成功后更新模型状态显示
            setTimeout(() => {
                this.updateModelStatusDisplay();
            }, 1000);

            // 初始化事件处理
            this.eventHandler.initialize();
            this.eventHandler.bindGlobalEvents();

            // 加载保存的设置
            this.loadSavedSettings();

            // 加载系统状态
            await this.loadSystemStatus();

            // 创建初始会话
            this.createNewSession();

            console.log('AgentChat 初始化完成');
        } catch (error) {
            console.error('AgentChat 初始化失败:', error);
            this.showNotification('系统初始化失败', 'error');
        }
    }

    // 发送消息
    async sendMessage(message) {
        // 清空输入框
        const chatInput = document.getElementById('chat-input');
        if (chatInput) {
            chatInput.value = '';
            this.eventHandler.handleInputChange();
        }

        // 添加用户消息
        this.sessionManager.addMessage('user', message);

        // 隐藏欢迎消息
        this.hideWelcomeMessage();

        // 立即渲染消息以显示用户输入
        this.renderMessages();

        // 立即创建一个空的助手消息用于后续更新
        const assistantMessage = this.sessionManager.addMessage('assistant', '', {
            isActive: true,
            toolExecutions: [],
            thinkingProcess: []
        });
        this.messageHandler.setCurrentAssistantMessageId(assistantMessage.id);

        // 显示打字指示器
        this.showTypingIndicator();

        // 设置处理状态
        this.eventHandler.setProcessingState(true);

        try {
            // 发送到后端
            await this.sendToBackend(message);
        } catch (error) {
            // 更新当前助手消息显示错误
            if (this.messageHandler.getCurrentAssistantMessageId()) {
                const session = this.sessionManager.getCurrentSession();
                const errorMessage = session.messages.find(m => m.id === this.messageHandler.getCurrentAssistantMessageId());
                if (errorMessage) {
                    errorMessage.content = `抱歉，发生了错误：${error.message}`;
                    errorMessage.metadata.isActive = false;
                    this.updateAssistantMessage(errorMessage);
                }
            }
            this.hideTypingIndicator();
            this.eventHandler.setProcessingState(false);
            this.messageHandler.clearCurrentAssistantMessageId();
            throw error;
        }
    }

    // 发送消息到后端
    async sendToBackend(message) {
        const response = await fetch('/api/task', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                task: message,
                session_id: this.sessionManager.currentSessionId,
                model: this.configManager.currentModel
            })
        });

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message);
        }
    }

    // 创建新会话
    createNewSession() {
        const session = this.sessionManager.createNewSession();
        this.renderMessages();
        this.showWelcomeMessage();
        return session;
    }

    // 切换会话
    switchToSession(sessionId) {
        const session = this.sessionManager.switchToSession(sessionId);
        if (session) {
            this.renderMessages();
        }
        return session;
    }

    // 清空当前会话
    clearCurrentSession() {
        const session = this.sessionManager.clearCurrentSession();
        if (session) {
            this.renderMessages();
            this.showWelcomeMessage();
            this.showNotification('会话已删除', 'info');
        }
        return session;
    }

    // 切换模型
    switchModel(newModel) {
        this.configManager.currentModel = newModel;

        // 显示模型切换通知
        this.showNotification(`已切换到模型: ${this.configManager.getModelDisplayName(newModel)}`, 'info');

        // 更新状态显示
        this.updateModelStatus('connecting');

        // 模拟模型加载时间
        setTimeout(() => {
            this.updateModelStatus('active');
            this.showNotification(`模型 ${this.configManager.getModelDisplayName(newModel)} 已就绪`, 'success');
        }, 2000);

        // 在当前会话中添加系统消息
        if (this.sessionManager.currentSessionId) {
            this.sessionManager.addMessage('system', `已切换到模型: ${this.configManager.getModelDisplayName(newModel)}`);
        }
    }

    // 渲染消息
    renderMessages() {
        const messages = this.sessionManager.getCurrentMessages();
        const messagesContainer = document.getElementById('chat-messages');

        if (!messagesContainer) return;

        if (messages.length === 0) {
            this.showWelcomeMessage();
            return;
        }

        // 清空容器并重新渲染所有消息
        messagesContainer.innerHTML = '';

        // 使用DocumentFragment批量添加，减少重排
        const fragment = document.createDocumentFragment();

        messages.forEach(message => {
            const messageHtml = this.messageRenderer.renderMessage(message);
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = messageHtml;
            const messageElement = tempDiv.firstElementChild;
            fragment.appendChild(messageElement);
        });

        messagesContainer.appendChild(fragment);

        // 滚动到底部
        this.smoothScrollToBottom();
    }

    // 增量添加单个消息到DOM
    appendMessage(message) {
        const messagesContainer = document.getElementById('chat-messages');
        const messageHtml = this.messageRenderer.renderMessage(message);

        // 创建临时容器
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = messageHtml;
        const messageElement = tempDiv.firstElementChild;

        // 添加到容器
        messagesContainer.appendChild(messageElement);

        // 平滑滚动到底部
        this.smoothScrollToBottom();
    }

    // 更新助手消息
    updateAssistantMessage(message) {
        const messageElement = document.querySelector(`[data-message-id="${message.id}"]`);
        if (!messageElement) {
            this.renderMessages();
            return;
        }

        // 重新渲染消息内容
        const newMessageHtml = this.messageRenderer.renderMessage(message);
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = newMessageHtml;
        const newMessageElement = tempDiv.firstElementChild;

        // 替换现有消息元素
        messageElement.parentNode.replaceChild(newMessageElement, messageElement);

        // 平滑滚动到底部
        this.smoothScrollToBottom();
    }

    // 平滑滚动到底部
    smoothScrollToBottom() {
        const messagesContainer = document.getElementById('chat-messages');
        if (messagesContainer) {
            messagesContainer.scrollTo({
                top: messagesContainer.scrollHeight,
                behavior: 'smooth'
            });
        }
    }

    // 显示欢迎消息
    showWelcomeMessage() {
        const messagesContainer = document.getElementById('chat-messages');
        if (messagesContainer) {
            messagesContainer.innerHTML = `
                <div class="welcome-message">
                    <div class="welcome-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3>欢迎使用 Agent 智能助手</h3>
                    <p>我可以帮您执行各种任务，包括网络搜索、文件操作、数学计算等。请告诉我您需要什么帮助。</p>
                    <div class="example-tasks">
                        <button class="example-task" data-task="搜索Python编程教程">搜索Python教程</button>
                        <button class="example-task" data-task="计算144的平方根">数学计算</button>
                        <button class="example-task" data-task="创建一个包含今天日期的文件">创建文件</button>
                        <button class="example-task" data-task="分析当前天气情况">天气分析</button>
                    </div>
                </div>
            `;
        }
    }

    // 隐藏欢迎消息
    hideWelcomeMessage() {
        const welcomeMessage = document.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
    }

    // 显示打字指示器
    showTypingIndicator() {
        const typingIndicator = document.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.style.display = 'inline';
        }
    }

    // 隐藏打字指示器
    hideTypingIndicator() {
        const typingIndicator = document.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.style.display = 'none';
        }
    }

    // 显示通知
    showNotification(message, type = 'info') {
        this.websocketManager.showNotification(message, type);
    }

    // 更新模型选择器
    updateModelSelectors(models) {
        const selectors = [
            document.getElementById('model-select'),
            document.getElementById('model-name')
        ];

        selectors.forEach(selector => {
            if (!selector) return;

            // 保存当前选中的值
            const currentValue = selector.value || this.configManager.currentModel;

            // 清空现有选项
            selector.innerHTML = '';

            // 添加新选项
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.name;
                option.textContent = model.description || model.name;
                selector.appendChild(option);
            });

            // 设置正确的选中状态
            if (models.some(m => m.name === this.configManager.currentModel)) {
                selector.value = this.configManager.currentModel;
            } else if (models.some(m => m.name === currentValue)) {
                selector.value = currentValue;
                this.configManager.currentModel = currentValue;
            } else if (models.length > 0) {
                selector.value = models[0].name;
                this.configManager.currentModel = models[0].name;
            }

            // 添加change事件监听器
            if (selector.id === 'model-name' && !selector.hasAttribute('data-listener-added')) {
                selector.addEventListener('change', (e) => this.switchModel(e.target.value));
                selector.setAttribute('data-listener-added', 'true');
            }
        });

        // 更新模型状态显示
        this.updateModelStatusDisplay();
    }

    // 更新模型状态显示
    updateModelStatusDisplay() {
        const modelStatusElement = document.querySelector('#model-tooltip');
        const currentModelName = this.configManager.getModelDisplayName(this.configManager.currentModel);

        if (modelStatusElement) {
            // 检查当前连接状态
            const statusDot = document.querySelector('#model-status .status-dot');
            let statusText = '连接中...';

            if (statusDot) {
                if (statusDot.classList.contains('active')) {
                    statusText = '模型就绪';
                } else if (statusDot.classList.contains('connected')) {
                    statusText = '已连接';
                } else if (statusDot.classList.contains('error')) {
                    statusText = '连接错误';
                } else if (statusDot.classList.contains('disconnected')) {
                    statusText = '已断开';
                }
            }

            modelStatusElement.textContent = `${currentModelName} - ${statusText}`;
        }

        const modelStatusContainer = document.querySelector('#model-status');
        if (modelStatusContainer) {
            modelStatusContainer.title = `当前模型: ${currentModelName}`;
        }

        // 同步更新模型选择器
        const modelSelect = document.getElementById('model-select');
        if (modelSelect && modelSelect.value !== this.configManager.currentModel) {
            modelSelect.value = this.configManager.currentModel;
        }
    }

    // 更新模型状态
    updateModelStatus(status) {
        this.websocketManager.updateConnectionStatus(status);
    }

    // 检查连接状态
    checkConnectionStatus() {
        if (!this.websocketManager.isConnected()) {
            this.websocketManager.initialize();
        }
    }

    // 加载保存的设置
    loadSavedSettings() {
        try {
            const savedSettings = localStorage.getItem('agentSettings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                this.eventHandler.applySettingsToUI(settings);
                this.eventHandler.applySettings(settings);
            }
        } catch (error) {
            console.error('加载设置失败:', error);
        }
    }

    // 加载系统状态
    async loadSystemStatus() {
        try {
            // 这里可以添加系统状态检查逻辑
            console.log('系统状态检查完成');
        } catch (error) {
            console.error('系统状态检查失败:', error);
        }
    }

    // 生成用户友好的回答
    generateUserFriendlyAnswer(message) {
        const toolExecutions = message.metadata.toolExecutions || [];

        // 根据工具类型生成不同的回答
        if (toolExecutions.length > 0) {
            const firstTool = toolExecutions[0];

            if (firstTool.tool === 'calculator' || firstTool.tool === 'CalculatorTool') {
                return this.generateCalculatorAnswer(firstTool, message);
            } else if (firstTool.tool === 'WebSearchTool') {
                return this.generateSearchAnswer(firstTool, message);
            } else if (firstTool.tool === 'FileOperationsTool') {
                return this.generateFileAnswer(firstTool, message);
            }
        }

        // 默认回答
        const taskSummary = message.metadata.taskSummary || '✅ 任务完成';
        return `${taskSummary}

我已经成功完成了您的任务！在执行过程中：

🔧 **执行了 ${toolExecutions.length} 个操作**
📊 **所有步骤均已完成**

您可以点击上方的"思维过程"和"工具执行"查看详细的执行步骤和结果。如果您需要进一步的帮助或有其他问题，请随时告诉我！`;
    }

    // 生成计算器回答
    generateCalculatorAnswer(toolExecution, message) {
        const expression = toolExecution.parameters?.expression || '';
        const result = toolExecution.result || '';
        const answerMatch = result.match(/答案:\s*([0-9.+-]+)/);
        const resultMatch = result.match(/结果:\s*\*\*([0-9.+-]+)\*\*/);

        let answer = null;
        if (answerMatch) {
            answer = answerMatch[1];
        } else if (resultMatch) {
            answer = resultMatch[1];
        }

        if (answer) {
            return `${expression} 的计算结果是 **${answer}**。

${message.metadata.taskSummary || '✅ 计算完成'}`;
        } else {
            return `我已经完成了 ${expression} 的计算。您可以在上方的工具执行结果中查看详细信息。

${message.metadata.taskSummary || '✅ 计算完成'}`;
        }
    }

    // 生成搜索回答
    generateSearchAnswer(toolExecution, message) {
        const query = toolExecution.parameters?.query || '';
        const result = toolExecution.result || '';

        const resultCountMatch = result.match(/找到结果:\s*(\d+)\s*个/);
        const resultCount = resultCountMatch ? resultCountMatch[1] : '多个';

        let answer = `我为您搜索了"${query}"，找到了 ${resultCount} 个高质量的学习资源。`;

        answer += `\n\n💡 **完整内容：** 点击上方"工具执行"查看详细的AI分析、学习路径建议和完整参考文献列表。

${message.metadata.taskSummary || '✅ 搜索完成'}`;

        return answer;
    }

    // 生成文件操作回答
    generateFileAnswer(toolExecution, message) {
        const action = toolExecution.action || '';
        const filename = toolExecution.parameters?.filename || '';

        if (action === 'create_file' && filename) {
            return `我已经成功创建了文件 "${filename}"。您可以在上方查看文件创建的详细信息。

${message.metadata.taskSummary || '✅ 文件创建完成'}`;
        }

        return `文件操作已完成。您可以在上方查看操作详情。

${message.metadata.taskSummary || '✅ 操作完成'}`;
    }

    // 清理资源
    cleanup() {
        this.eventHandler.cleanup();
        this.websocketManager.close();
        this.messageRenderer.clearAllTypewriterEffects();
    }
}

// 导出AgentChat类
window.AgentChat = AgentChat;
