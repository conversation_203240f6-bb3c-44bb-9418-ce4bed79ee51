"""
MCP Client implementation for connecting to MCP servers
"""

import asyncio
import json
import uuid
from typing import Dict, AsyncGenerator
import httpx
from rich.console import Console


class MCPClient:
    """Client for connecting to MCP servers"""
    
    def __init__(self, server_url: str = "http://localhost:8000"):
        self.server_url = server_url.rstrip('/')
        self.client = httpx.AsyncClient(timeout=30.0)
        self.console = Console()
        self.client_id = str(uuid.uuid4())
    
    async def list_tools(self) -> Dict:
        """List available tools from the MCP server"""
        try:
            response = await self.client.get(f"{self.server_url}/tools")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.console.print(f"❌ Failed to list tools: {e}")
            return {"tools": []}
    
    async def execute_tool(self, tool_name: str, action: str, parameters: Dict) -> Dict:
        """Execute a tool via MCP server"""
        
        request_data = {
            "method": f"{tool_name}.{action}",
            "params": parameters,
            "id": str(uuid.uuid4())
        }
        
        try:
            response = await self.client.post(
                f"{self.server_url}/execute",
                json=request_data
            )
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            self.console.print(f"❌ Failed to execute tool: {e}")
            return {"error": str(e)}
    
    async def stream_execute_tool(self, tool_name: str, action: str, parameters: Dict) -> AsyncGenerator[Dict, None]:
        """Execute tool and stream results via SSE"""
        
        request_data = {
            "method": f"{tool_name}.{action}",
            "params": parameters,
            "id": str(uuid.uuid4())
        }
        
        try:
            # Start streaming connection
            async with self.client.stream(
                "GET", 
                f"{self.server_url}/stream/{self.client_id}"
            ) as stream_response:
                
                # Start execution in background
                execute_task = asyncio.create_task(
                    self._trigger_stream_execution(request_data)
                )
                
                # Process SSE events
                async for line in stream_response.aiter_lines():
                    if line.startswith("data: "):
                        try:
                            data = json.loads(line[6:])  # Remove "data: " prefix
                            yield data
                        except json.JSONDecodeError:
                            continue
                    elif line.startswith("event: "):
                        event_type = line[7:]  # Remove "event: " prefix
                        # Event type is handled in the data processing
                        continue
                
                # Wait for execution to complete
                await execute_task
                
        except Exception as e:
            self.console.print(f"❌ Failed to stream execute tool: {e}")
            yield {"error": str(e)}
    
    async def _trigger_stream_execution(self, request_data: Dict):
        """Trigger tool execution for streaming"""
        try:
            response = await self.client.post(
                f"{self.server_url}/stream/{self.client_id}/execute",
                json=request_data
            )
            response.raise_for_status()
        except Exception as e:
            self.console.print(f"❌ Failed to trigger stream execution: {e}")
    
    async def test_connection(self) -> bool:
        """Test connection to MCP server"""
        try:
            response = await self.client.get(f"{self.server_url}/")
            response.raise_for_status()
            data = response.json()
            self.console.print(f"✅ Connected to MCP server: {data.get('message', 'Unknown')}")
            return True
        except Exception as e:
            self.console.print(f"❌ Failed to connect to MCP server: {e}")
            return False
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
