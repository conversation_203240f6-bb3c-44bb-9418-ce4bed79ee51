"""
Logging Configuration for CyberAgent

This module provides centralized logging configuration for the entire framework.
"""

import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Optional


class LoggingConfig:
    """
    Basic Logging Configuration.
    Primary file logging should be handled by TaskLogger.
    This class provides a way to get basic logger instances,
    but they will not write to files by default anymore.
    """
    
    # Default log directory - No longer used for file creation here
    # DEFAULT_LOG_DIR = "logs"
    
    # Log format
    DEFAULT_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    @classmethod
    def setup_logger(
        cls,
        name: str,
        log_level: int = logging.INFO,
        # log_dir: Optional[str] = None, # No longer used for file creation
        # include_timestamp: bool = True, # No longer used for file creation
        # file_prefix: str = "cyberagent" # No longer used for file creation
    ) -> logging.Logger:
        """
        Setup a basic logger instance.
        It will NOT have file output by default. File output is managed by TaskLogger.
        """
        logger = logging.getLogger(name)
        logger.setLevel(log_level)
        
        # Clear existing handlers to avoid duplicates from previous configurations
        # or inherited handlers if not setting propagate=False earlier.
        logger.handlers.clear()
        
        # Add a NullHandler to prevent "No handlers could be found" messages
        # if no other handler is configured later (e.g., by TaskLogger).
        # This also ensures that if this logger is used directly without TaskLogger,
        # it won't attempt to write files or output to console unless explicitly configured.
        if not logger.hasHandlers(): # Should be true after clear()
            null_handler = logging.NullHandler()
            logger.addHandler(null_handler)
            
        # Prevent propagation to the root logger, as TaskLogger will handle specific outputs.
        logger.propagate = False
        
        # No file handler is created here.
        # logger.info(f"Basic logger '{name}' initialized (configured with NullHandler).")
        
        return logger
    
    # @classmethod
    # def setup_execution_logger(cls) -> logging.Logger:
    #     """DEPRECATED: Use TaskLogger.execution_logger instead."""
    #     # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    #     # return cls.setup_logger(
    #     #     name=f"ExecutionEngine_{timestamp}",
    #     # )
    #     logging.warning("setup_execution_logger is deprecated. Use TaskLogger from cyberagent.core.task_logger.")
    #     return cls.setup_logger(name="DEPRECATED_ExecutionEngine")


    # @classmethod
    # def setup_security_logger(cls) -> logging.Logger:
    #     """DEPRECATED: Use TaskLogger for security logs instead."""
    #     # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    #     # return cls.setup_logger(
    #     #     name=f"SecurityOps_{timestamp}",
    #     # )
    #     logging.warning("setup_security_logger is deprecated. Use TaskLogger from cyberagent.core.task_logger.")
    #     return cls.setup_logger(name="DEPRECATED_SecurityOps")

    # @classmethod
    # def setup_investigation_logger(cls, investigation_id: str) -> logging.Logger:
    #     """DEPRECATED: Use TaskLogger for investigation logs instead."""
    #     # return cls.setup_logger(
    #     #     name=f"Investigation_{investigation_id}",
    #     # )
    #     logging.warning("setup_investigation_logger is deprecated. Use TaskLogger from cyberagent.core.task_logger.")
    #     return cls.setup_logger(name=f"DEPRECATED_Investigation_{investigation_id}")
    
    # @classmethod
    # def cleanup_old_logs(cls, log_dir: Optional[str] = None, days_to_keep: int = 30):
    #     """
    #     DEPRECATED: Log cleanup should be managed by TaskLoggerManager or a similar mechanism
    #     that understands the new log structure in 'logs/tasks/'.
    #     """
    #     # if log_dir is None:
    #     #     log_dir = cls.DEFAULT_LOG_DIR # This is now problematic
    #     #
    #     # log_path = Path(log_dir)
    #     # if not log_path.exists():
    #     #     return
    #     #
    #     # cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 3600)
    #     #
    #     # for log_file in log_path.glob("*.log"):
    #     #     try:
    #     #         if log_file.stat().st_mtime < cutoff_time:
    #     #             log_file.unlink()
    #     #             print(f"🗑️ Cleaned up old log file: {log_file.name}")
    #     #     except Exception as e:
    #     #         print(f"⚠️ Failed to clean up log file {log_file.name}: {e}")
    #     logging.warning("cleanup_old_logs is deprecated and non-functional for TaskLogger structure.")
    #     pass


def get_logger(name: str, **kwargs) -> logging.Logger:
    """
    Convenience function to get a basic logger instance.
    This logger will NOT have file output by default. File output is managed by TaskLogger.
    Consider using TaskLogger from cyberagent.core.task_logger for task-specific logging.
    """
    # Remove unused kwargs to avoid passing them to the new setup_logger
    kwargs.pop('log_dir', None)
    kwargs.pop('include_timestamp', None)
    kwargs.pop('file_prefix', None)
    
    # logging.info(f"Acquiring basic logger '{name}'. For file logging, use TaskLogger.")
    return LoggingConfig.setup_logger(name, **kwargs)
