"""
MCP Server startup script
"""

import sys
from pathlib import Path
import uvicorn

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from execution.tools.manager import ToolManager
from execution.mcp.server import MCPServer


def create_app():
    """Create and configure the MCP server app"""
    tool_manager = ToolManager()
    mcp_server = MCPServer(tool_manager)
    return mcp_server.get_app()


if __name__ == "__main__":
    app = create_app()

    print("🚀 Starting MCP Tool Server...")
    print("📡 Server will be available at: http://localhost:8000")
    print("📋 API docs at: http://localhost:8000/docs")
    print("🔧 Tools endpoint: http://localhost:8000/tools")

    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
