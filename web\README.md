# Agent 监控面板

一个现代化的前端界面，用于监控和展示Agent工具的运行情况，包括任务规划、工具调用、COT思维过程等。

## 🎨 设计特色

- **iOS风格设计**: 采用苹果设计语言，简洁现代
- **浅色主题**: 清爽的浅色配色方案
- **流畅动画**: 丰富的过渡动画和交互效果
- **响应式布局**: 适配各种屏幕尺寸
- **实时更新**: 通过WebSocket实时显示执行状态

## 🚀 功能特性

### 主要模块

1. **任务输入区域**
   - 用户友好的任务输入界面
   - 支持Ctrl+Enter快捷提交

2. **任务规划展示**
   - 实时显示TaskPlanner生成的执行计划
   - 步骤状态可视化（等待中、进行中、已完成、失败）

3. **COT思维过程**
   - 可折叠的模型思考过程展示
   - 实时显示AI的推理步骤

4. **执行监控**
   - 进度条显示执行进度
   - 详细的步骤执行状态

5. **工具调用详情**
   - 显示每个工具的调用参数和结果
   - 执行时间戳记录

6. **会话历史**
   - 完整的对话历史记录
   - 用户消息和助手回复的区分显示

7. **系统状态指示器**
   - 模型连接状态
   - 工具系统状态
   - 实时状态更新

## 📦 安装和运行

### 方法1: 使用启动脚本（推荐）

```bash
# 进入前端目录
cd demo/frontend

# 运行启动脚本（会自动安装依赖）
python start_server.py
```

### 方法2: 手动安装

```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务器
python api_server.py
```

### 访问界面

启动成功后，在浏览器中访问：
```
http://127.0.0.1:8000
```

## 🛠️ 技术架构

### 前端技术栈
- **HTML5**: 语义化标记
- **CSS3**: 现代CSS特性，CSS变量，动画
- **JavaScript ES6+**: 原生JavaScript，WebSocket通信
- **Font Awesome**: 图标库

### 后端技术栈
- **FastAPI**: 现代Python Web框架
- **WebSocket**: 实时双向通信
- **Uvicorn**: ASGI服务器
- **Pydantic**: 数据验证

### 集成组件
- **OllamaClient**: AI模型客户端
- **TaskPlanner**: 任务规划器
- **ExecutionEngine**: 执行引擎
- **ToolManager**: 工具管理器
- **SessionManager**: 会话管理器

## 🎯 使用说明

### 基本使用流程

1. **启动服务器**
   ```bash
   python start_server.py
   ```

2. **打开浏览器**
   - 访问 http://127.0.0.1:8000
   - 等待WebSocket连接建立（状态指示器变为绿色）

3. **输入任务**
   - 在任务输入框中描述您的需求
   - 点击"执行任务"按钮或使用Ctrl+Enter

4. **观察执行过程**
   - 任务规划：查看AI生成的执行步骤
   - COT思维：展开查看AI的思考过程
   - 执行监控：实时查看执行进度
   - 工具调用：查看具体的工具使用情况

### 示例任务

```
搜索Python编程教程
计算144的平方根
创建一个包含今天日期的文件
分析当前天气情况
```

## 🎨 界面说明

### 状态指示器
- 🟢 **绿色**: 正常运行
- 🟡 **黄色**: 连接中/警告
- 🔴 **红色**: 错误/断开连接

### 可折叠区域
- 点击区域标题可展开/折叠内容
- 默认展开：任务规划、执行监控
- 默认折叠：COT思维、工具调用、会话历史

### 动画效果
- **加载动画**: 任务处理时的旋转加载器
- **进度动画**: 平滑的进度条更新
- **状态动画**: 步骤状态变化的过渡效果
- **通知动画**: 右上角的通知弹出效果

## 🔧 配置说明

### 服务器配置
在 `api_server.py` 中可以修改：
- 服务器端口（默认8000）
- WebSocket连接设置
- 静态文件路径

### 样式配置
在 `styles.css` 中可以自定义：
- 颜色主题（CSS变量）
- 动画时长和效果
- 布局间距和尺寸

## 🐛 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查服务器是否正常启动
   - 确认端口8000未被占用
   - 检查防火墙设置

2. **模型连接失败**
   - 确认Ollama服务正在运行
   - 检查模型配置（默认qwen3:32b）
   - 验证网络连接

3. **依赖安装失败**
   - 使用虚拟环境
   - 更新pip版本
   - 检查Python版本（需要3.8+）

### 调试模式

在浏览器开发者工具中查看：
- Console: JavaScript错误和WebSocket消息
- Network: API请求状态
- WebSocket: 实时通信数据

## 📝 开发说明

### 文件结构
```
demo/frontend/
├── index.html          # 主页面
├── styles.css          # 样式文件
├── script.js           # JavaScript逻辑
├── api_server.py       # 后端API服务器
├── start_server.py     # 启动脚本
├── requirements.txt    # Python依赖
└── README.md          # 说明文档
```

### 扩展开发

1. **添加新的UI组件**
   - 在HTML中添加结构
   - 在CSS中定义样式
   - 在JavaScript中添加交互逻辑

2. **集成新的工具**
   - 在后端注册新工具
   - 更新前端显示逻辑
   - 添加相应的状态处理

3. **自定义主题**
   - 修改CSS变量
   - 调整颜色和字体
   - 更新动画效果

## 📄 许可证

本项目遵循MIT许可证。
