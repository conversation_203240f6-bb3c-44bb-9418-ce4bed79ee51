// 主入口文件 - 初始化应用
(function() {
    'use strict';

    // 全局变量
    let agentChat = null;

    // 等待DOM加载完成
    function waitForDOM() {
        return new Promise((resolve) => {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', resolve);
            } else {
                resolve();
            }
        });
    }

    // 检查依赖是否加载完成
    function checkDependencies() {
        const requiredClasses = [
            'ConfigManager',
            'SessionManager', 
            'MessageRenderer',
            'MessageHandler',
            'EventHandler',
            'WebSocketManager',
            'AgentChat'
        ];

        const missingClasses = requiredClasses.filter(className => !window[className]);
        
        if (missingClasses.length > 0) {
            console.error('缺少依赖类:', missingClasses);
            return false;
        }
        
        return true;
    }

    // 初始化应用
    async function initializeApp() {
        try {
            console.log('开始初始化 AgentChat 应用...');

            // 等待DOM加载
            await waitForDOM();
            console.log('DOM 加载完成');

            // 检查依赖
            if (!checkDependencies()) {
                throw new Error('依赖检查失败，请确保所有模块文件都已正确加载');
            }
            console.log('依赖检查通过');

            // 创建AgentChat实例
            agentChat = new AgentChat();
            
            // 将实例暴露到全局作用域（用于调试）
            window.agentChat = agentChat;

            console.log('AgentChat 应用初始化成功');

        } catch (error) {
            console.error('AgentChat 应用初始化失败:', error);
            
            // 显示错误信息给用户
            showInitializationError(error);
        }
    }

    // 显示初始化错误
    function showInitializationError(error) {
        const errorContainer = document.createElement('div');
        errorContainer.className = 'initialization-error';
        errorContainer.innerHTML = `
            <div class="error-content">
                <h3>应用初始化失败</h3>
                <p>抱歉，应用无法正常启动。请刷新页面重试。</p>
                <details>
                    <summary>错误详情</summary>
                    <pre>${error.message}</pre>
                </details>
                <button onclick="location.reload()" class="retry-button">刷新页面</button>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .initialization-error {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            .error-content {
                background: white;
                padding: 2rem;
                border-radius: 8px;
                max-width: 500px;
                text-align: center;
            }
            .error-content h3 {
                color: #dc3545;
                margin-bottom: 1rem;
            }
            .error-content details {
                margin: 1rem 0;
                text-align: left;
            }
            .error-content pre {
                background: #f8f9fa;
                padding: 1rem;
                border-radius: 4px;
                overflow: auto;
                max-height: 200px;
            }
            .retry-button {
                background: #007bff;
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 4px;
                cursor: pointer;
                font-size: 1rem;
            }
            .retry-button:hover {
                background: #0056b3;
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(errorContainer);
    }

    // 页面卸载时清理资源
    function cleanup() {
        if (agentChat) {
            agentChat.cleanup();
        }
    }

    // 绑定页面卸载事件
    window.addEventListener('beforeunload', cleanup);
    window.addEventListener('unload', cleanup);

    // 处理未捕获的错误
    window.addEventListener('error', (event) => {
        console.error('未捕获的错误:', event.error);
    });

    // 处理未捕获的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
        console.error('未处理的Promise拒绝:', event.reason);
    });

    // 开发模式下的调试功能
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        // 添加调试快捷键
        document.addEventListener('keydown', (event) => {
            // Ctrl+Shift+D 切换调试模式
            if (event.ctrlKey && event.shiftKey && event.key === 'D') {
                if (agentChat && agentChat.configManager) {
                    agentChat.configManager.debugMode = !agentChat.configManager.debugMode;
                    console.log('调试模式:', agentChat.configManager.debugMode ? '开启' : '关闭');
                }
            }
            
            // Ctrl+Shift+R 重新初始化
            if (event.ctrlKey && event.shiftKey && event.key === 'R') {
                console.log('重新初始化应用...');
                if (agentChat) {
                    agentChat.cleanup();
                }
                setTimeout(() => {
                    location.reload();
                }, 100);
            }
        });

        // 暴露调试函数到全局作用域
        window.debugAgentChat = {
            getConfig: () => agentChat?.configManager,
            getSessions: () => agentChat?.sessionManager.getAllSessions(),
            getWebSocketState: () => agentChat?.websocketManager.getConnectionState(),
            exportSessions: () => agentChat?.sessionManager.exportSessions(),
            importSessions: (data) => agentChat?.sessionManager.importSessions(data),
            clearAllSessions: () => agentChat?.sessionManager.clearAllSessions(),
            toggleDebugMode: () => {
                if (agentChat?.configManager) {
                    agentChat.configManager.debugMode = !agentChat.configManager.debugMode;
                    console.log('调试模式:', agentChat.configManager.debugMode ? '开启' : '关闭');
                }
            }
        };

        console.log('开发模式已启用，可使用 window.debugAgentChat 进行调试');
        console.log('快捷键: Ctrl+Shift+D 切换调试模式, Ctrl+Shift+R 重新初始化');
    }

    // 启动应用
    initializeApp();

})();
