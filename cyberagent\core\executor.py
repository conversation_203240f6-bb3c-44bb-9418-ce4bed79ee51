"""
Execution Engine - Responsible for executing planned tasks step by step
"""

import asyncio
import logging
import os
from datetime import datetime
from typing import Dict, Optional, List
from rich.console import Console
from rich.progress import Progress, TaskID
from rich.table import Table
from rich.panel import Panel

from .planner import TaskPlan, TaskStep, TaskStatus
from .logging_config import get_logger as get_config_logger # Renamed import for clarity
from .task_logger import TaskLogger, get_task_logger # Keep for actual task logs
from execution.tools.manager import ToolManager


class ExecutionEngine:
    """
    Executes task plans step by step using available tools
    """

    def __init__(
        self,
        tool_manager: ToolManager,
        websocket_callback=None,
        task_logger: TaskLogger = None,
        model_client=None,
    ):
        self.tool_manager = tool_manager
        self.console = Console()
        self.active_executions: Dict[str, bool] = {}
        self.execution_logs: List[str] = []
        self.websocket_callback = websocket_callback  # 用于Web模式的实时更新
        self.task_logger = task_logger  # 任务级别的日志记录器, may be None
        self.model_client = model_client  # 用于生成智能回复

        # Determine the target logger for execution messages
        if self.task_logger:
            self.execution_log_target = self.task_logger # The _log method will call task_logger.log_execution
            # self.task_logger.log_execution("ExecutionEngine initialized with provided task_logger.")
        else:
            # If no task_logger is provided, use a non-file-writing logger for internal/system messages.
            # This logger comes from logging_config and will have a NullHandler by default.
            self.execution_log_target = get_config_logger("ExecutionEngine_Internal")
            self.execution_log_target.info("ExecutionEngine initialized without specific task_logger. Internal logs will not create EXECUTION_ENGINE_INTERNAL folder.")

    async def execute_plan(self, plan: TaskPlan) -> bool:
        """Execute a complete task plan"""

        if plan.id in self.active_executions:
            self.console.print(f"❌ 计划 {plan.id} 正在执行中")
            self._log(f"❌ 计划 {plan.id} 已在执行中，跳过重复执行", "WARNING")
            return False

        self.active_executions[plan.id] = True
        plan.status = TaskStatus.IN_PROGRESS

        self.console.print(f"🚀 开始自动执行计划: {plan.description}")
        self._log(f"🚀 开始执行计划 - 计划ID: {plan.id}, 描述: {plan.description}")

        # 发送执行开始的WebSocket消息
        await self._send_websocket_message(
            {"type": "execution_start", "data": {"plan_id": plan.id}}
        )

        try:
            # 在终端模式显示进度条，Web模式跳过
            if not self.websocket_callback:
                progress = Progress()
                progress.start()
                task_id = progress.add_task(
                    f"执行中 {plan.description}", total=len(plan.steps)
                )
            else:
                progress = None
                task_id = None

            for i, step in enumerate(plan.steps):
                # 更新进度条描述（仅终端模式）
                if progress:
                    progress.update(
                        task_id,
                        description=f"步骤 {step.id}/{len(plan.steps)}: {step.description[:30]}...",
                    )

                self.console.print(f"\n📍 步骤 {step.id}: {step.description}")
                self._log(f"📍 执行步骤 {i + 1}/{len(plan.steps)} - 步骤ID: {step.id}")
                self._log(f"🔧 工具: {step.tool}, 动作: {step.action}")
                self._log(f"📝 描述: {step.description}")
                self._log(f"📋 参数: {step.parameters}")

                # 标记步骤开始
                step.status = TaskStatus.IN_PROGRESS

                # 发送步骤开始的WebSocket消息
                await self._send_websocket_message(
                    {
                        "type": "step_start",
                        "data": {
                            "step_id": step.id,
                            "status": step.status.value,
                            "progress": (i / len(plan.steps)) * 100,
                        },
                    }
                )

                success = await self.execute_step(step)

                if success:
                    step.status = TaskStatus.COMPLETED
                    self.console.print(f"✅ 步骤 {step.id} 完成")
                    self._log(
                        f"✅ 步骤 {i + 1}/{len(plan.steps)} 执行完成 - 步骤ID: {step.id}"
                    )
                    result_length = len(step.result) if step.result else 0
                    self._log(f"📊 执行结果长度: {result_length} 字符")
                    if step.result:
                        self._log(f"📋 结果预览: {step.result[:200]}...")
                else:
                    step.status = TaskStatus.FAILED
                    self.console.print(f"❌ 步骤 {step.id} 失败: {step.error}")
                    self._log(
                        f"❌ 步骤 {i + 1}/{len(plan.steps)} 执行失败 - 步骤ID: {step.id}, 错误: {step.error}",
                        "ERROR",
                    )

                    # 自动继续执行下一步
                    await self._handle_step_failure(step)

                # 发送步骤完成的WebSocket消息
                await self._send_websocket_message(
                    {
                        "type": "step_complete",
                        "data": {
                            "step_id": step.id,
                            "status": step.status.value,
                            "result": step.result,
                            "progress": ((i + 1) / len(plan.steps)) * 100,
                        },
                    }
                )

                # 精确更新进度（仅终端模式）
                if progress:
                    progress.update(task_id, completed=i + 1)
                await asyncio.sleep(0.2)  # 减少延迟

            # 停止进度条（仅终端模式）
            if progress:
                progress.stop()

            plan.status = TaskStatus.COMPLETED
            self.console.print(f"🎉 计划执行完成!")
            self._log(f"🎉 计划执行完成 - 计划ID: {plan.id}")

            # 生成智能回复
            intelligent_response = await self._generate_intelligent_response(plan)

            # 发送执行完成的WebSocket消息
            await self._send_websocket_message(
                {
                    "type": "execution_complete",
                    "data": {
                        "plan_id": plan.id,
                        "status": plan.status.value,
                        "intelligent_response": intelligent_response,
                        "summary": f"任务执行完成，共完成 {len(plan.steps)} 个步骤",
                    },
                }
            )

            return True

        except Exception as e:
            plan.status = TaskStatus.FAILED
            self.console.print(f"💥 计划执行失败，错误: {str(e)}")
            self._log(f"❌ 计划执行失败 - 计划ID: {plan.id}, 错误: {str(e)}", "ERROR")

            # 发送执行错误的WebSocket消息
            await self._send_websocket_message(
                {
                    "type": "execution_error",
                    "data": {"plan_id": plan.id, "error": str(e)},
                }
            )

            return False

        finally:
            self.active_executions.pop(plan.id, None)

    async def execute_step(self, step: TaskStep) -> bool:
        """Execute a single step"""

        step.status = TaskStatus.IN_PROGRESS
        self._log(f"⚡ 开始执行工具调用 - {step.tool}.{step.action}")

        try:
            # 获取适当的工具
            tool = self.tool_manager.get_tool(step.tool)
            if not tool:
                step.error = f"工具 '{step.tool}' 未找到"
                self._log(f"❌ 工具未找到: {step.tool}", "ERROR")
                return False

            self._log(f"🔧 工具分发 - 工具: {step.tool}, 动作: {step.action}")

            # 执行工具
            result = await tool.execute(step.action, step.parameters)
            step.result = str(result)

            # 使用任务日志记录器记录工具执行
            if self.task_logger:
                self.task_logger.log_tool_execution(
                    tool_name=step.tool,
                    action=step.action,
                    parameters=step.parameters,
                    result=result,
                )

            result_length = len(step.result) if step.result else 0
            self._log(f"💾 工具执行结果已保存 - 长度: {result_length} 字符")

            # 发送工具调用完成的WebSocket消息
            await self._send_websocket_message(
                {
                    "type": "tool_call",
                    "data": {
                        "tool": step.tool,
                        "action": step.action,
                        "parameters": step.parameters,
                        "result": step.result,
                        "timestamp": "实际执行时间",
                        "execution_time": "实际执行",
                        "status": "completed",
                    },
                }
            )

            return True

        except Exception as e:
            step.error = str(e)

            # 使用任务日志记录器记录工具执行错误
            if self.task_logger:
                self.task_logger.log_tool_execution(
                    tool_name=step.tool,
                    action=step.action,
                    parameters=step.parameters,
                    error=str(e),
                )

            self._log(
                f"❌ 工具执行失败 - {step.tool}.{step.action}, 错误: {str(e)}", "ERROR"
            )
            return False

    async def _handle_step_failure(self, step: TaskStep) -> bool:
        """处理步骤失败 - 自动继续执行"""
        self.console.print(f"⚠️  步骤失败但自动继续执行下一步...")
        self._log(f"步骤失败处理: {step.description} - {step.error}")
        return True

    def _log(self, message: str, level: str = "INFO"):
        """记录执行日志"""
        self.execution_logs.append(message)

        # Always use the determined execution_log_target
        # The TaskLogger's log_execution method itself handles logging to its specific execution_logger
        # and potentially to a full_logger. If execution_log_target is a logger instance directly,
        # we call its method.
        # If self.task_logger is not None, self.task_logger.log_execution would be more comprehensive
        # as it logs to both execution and full logs.
        # If self.task_logger is None, then self.execution_log_target is an actual logger instance.

        # The self.execution_log_target is either a TaskLogger instance or a basic Logger instance.
        if isinstance(self.execution_log_target, TaskLogger):
            self.execution_log_target.log_execution(message, level)
        else: # It's a basic logger (e.g., with NullHandler)
            getattr(self.execution_log_target, level.lower())(message)

    async def _generate_intelligent_response(self, plan: TaskPlan) -> str:
        """生成智能回复 - 使用LLM生成更智能的回复"""
        try:
            # 如果没有模型客户端，使用简单回复
            if not self.model_client:
                return self._generate_simple_response(plan)

            # 获取用户的原始问题
            user_task = getattr(plan, "description", "用户任务")
            self._log(f"🤖 智能回复生成 - 用户任务: {user_task}")

            # 收集所有工具执行结果
            tool_results = []
            for step in plan.steps:
                if hasattr(step, "result") and step.result:
                    tool_results.append(
                        {
                            "tool": step.tool,
                            "action": step.action,
                            "parameters": step.parameters,
                            "result": step.result,
                        }
                    )

            self._log(f"🔧 收集到 {len(tool_results)} 个工具执行结果")

            # 使用LLM生成智能回复
            response = await self._generate_llm_response(user_task, tool_results)
            if response and response.strip():
                self._log(f"💬 生成的智能回复: {response[:100]}...")
                return response.strip()
            else:
                # 如果LLM回复为空，降级到简单回复
                return self._generate_simple_response(plan)

        except Exception as e:
            self._log(f"❌ 生成智能回复失败: {str(e)}", "WARNING")
            # 降级到简单回复
            return self._generate_simple_response(plan)

    def _generate_simple_response(self, plan: TaskPlan) -> str:
        """生成简单的智能回复（降级方案）"""
        try:
            # 收集执行结果
            tool_results = []
            for step in plan.steps:
                if step.result:
                    tool_results.append(
                        {
                            "tool": step.tool,
                            "action": step.action,
                            "result": step.result[:200] + "..."
                            if len(step.result) > 200
                            else step.result,
                        }
                    )

            # 生成简单的智能回复
            if tool_results:
                first_tool = tool_results[0]
                if first_tool["tool"] == "web_search":
                    return f"我已经为您搜索了相关信息，找到了多个有用的资源。您可以查看上方的工具执行结果获取详细信息。"
                elif first_tool["tool"] == "calculator":
                    return f"计算已完成。您可以查看上方的工具执行结果获取详细的计算过程和结果。"
                elif first_tool["tool"] == "file_ops":
                    return f"文件操作已完成。您可以查看上方的工具执行结果获取详细信息。"
                else:
                    return f"任务已成功完成，共执行了 {len(plan.steps)} 个步骤。您可以查看上方的工具执行结果获取详细信息。"
            else:
                return "任务执行完成，但没有生成具体结果。"

        except Exception as e:
            self._log(f"⚠️ 生成简单回复失败: {str(e)}", "WARNING")
            return "任务执行完成。"

    async def _generate_llm_response(self, user_task: str, tool_results: list) -> str:
        """使用LLM生成智能回复"""
        try:
            # 构建上下文信息
            context = self._build_response_context(user_task, tool_results)

            # 构建提示词
            prompt = f"""你是一个智能助手，需要根据用户的问题和工具执行结果，生成一个自然、有用的回复。

用户问题：{user_task}

工具执行结果：
{context}

请根据以上信息，生成一个简洁、准确、有用的回复给用户。回复应该：
1. 直接回答用户的问题
2. 总结关键信息
3. 语言自然流畅
4. 避免重复工具执行的技术细节

回复："""

            # 调用模型生成回复
            response = await self.model_client.chat(
                [{"role": "user", "content": prompt}]
            )

            if response and response.strip():
                self._log(f"🤖 LLM智能回复生成成功: {response[:100]}...")
                return response.strip()
            else:
                self._log("⚠️ LLM返回空回复", "WARNING")
                return None

        except Exception as e:
            self._log(f"❌ LLM回复生成失败: {str(e)}", "WARNING")
            return None

    def _build_response_context(self, user_task: str, tool_results: list) -> str:
        """构建回复上下文"""
        if not tool_results:
            return "没有工具执行结果"

        context_parts = []
        for i, result in enumerate(tool_results, 1):
            tool_name = result.get("tool", "未知工具")
            action = result.get("action", "未知操作")
            result_text = result.get("result", "无结果")

            # 限制结果长度以避免上下文过长
            if len(result_text) > 500:
                result_text = result_text[:500] + "..."

            context_parts.append(f"工具{i}: {tool_name}.{action}\n结果: {result_text}")

        return "\n\n".join(context_parts)

    async def _send_websocket_message(self, message: dict):
        """发送WebSocket消息（如果有回调函数）"""
        if self.websocket_callback:
            try:
                await self.websocket_callback(message)
            except Exception as e:
                self._log(f"⚠️ WebSocket消息发送失败: {str(e)}", "WARNING")

    def get_execution_logs(self) -> List[str]:
        """获取执行日志"""
        return self.execution_logs.copy()

    def clear_logs(self):
        """清空日志"""
        self.execution_logs.clear()

    def get_execution_status(self, plan_id: str) -> Optional[Dict]:
        """Get the current execution status"""
        if plan_id in self.active_executions:
            return {
                "plan_id": plan_id,
                "is_running": self.active_executions[plan_id],
                "status": "in_progress",
            }
        return None

    def display_plan_status(self, plan: TaskPlan):
        """显示计划状态的表格"""

        table = Table(title=f"计划: {plan.description}")
        table.add_column("步骤", style="cyan", no_wrap=True)
        table.add_column("描述", style="white")
        table.add_column("工具", style="yellow")
        table.add_column("状态", style="green")
        table.add_column("结果", style="blue")

        for step in plan.steps:
            status_emoji = {
                TaskStatus.PENDING: "⏳",
                TaskStatus.IN_PROGRESS: "🔄",
                TaskStatus.COMPLETED: "✅",
                TaskStatus.FAILED: "❌",
            }.get(step.status, "❓")

            result_text = (
                step.result[:50] + "..."
                if step.result and len(step.result) > 50
                else (step.result or "")
            )

            # 状态中文映射
            status_text = {
                TaskStatus.PENDING: "等待中",
                TaskStatus.IN_PROGRESS: "执行中",
                TaskStatus.COMPLETED: "已完成",
                TaskStatus.FAILED: "失败",
            }.get(step.status, "未知")

            table.add_row(
                str(step.id),
                step.description,
                step.tool,
                f"{status_emoji} {status_text}",
                result_text,
            )

        self.console.print(table)

    async def execute_plan_streaming(
        self, plan: TaskPlan, session_id: str = None
    ) -> bool:
        """Execute a complete task plan with streaming updates"""

        if plan.id in self.active_executions:
            self.console.print(f"❌ 计划 {plan.id} 正在执行中")
            self._log(f"❌ 计划 {plan.id} 已在执行中，跳过重复执行", "WARNING")
            return False

        self.active_executions[plan.id] = True
        plan.status = TaskStatus.IN_PROGRESS

        self.console.print(f"🚀 开始流式执行计划: {plan.description}")
        self._log(f"🚀 开始流式执行计划 - 计划ID: {plan.id}, 描述: {plan.description}")

        # 发送执行开始的WebSocket消息
        await self._send_websocket_message(
            {
                "type": "execution_start",
                "data": {
                    "plan_id": plan.id,
                    "session_id": session_id,
                    "total_steps": len(plan.steps),
                    "message": f"开始执行 {len(plan.steps)} 个步骤",
                },
            }
        )

        try:
            # 不使用进度条，改为实时WebSocket更新
            for i, step in enumerate(plan.steps):
                self.console.print(f"\n📍 步骤 {step.id}: {step.description}")
                self._log(f"📍 执行步骤 {i + 1}/{len(plan.steps)} - 步骤ID: {step.id}")
                self._log(f"🔧 工具: {step.tool}, 动作: {step.action}")
                self._log(f"📝 描述: {step.description}")
                self._log(f"📋 参数: {step.parameters}")

                # 标记步骤开始
                step.status = TaskStatus.IN_PROGRESS

                # 发送步骤开始的WebSocket消息
                await self._send_websocket_message(
                    {
                        "type": "step_start",
                        "data": {
                            "plan_id": plan.id,
                            "session_id": session_id,
                            "step_id": step.id,
                            "step_number": i + 1,
                            "total_steps": len(plan.steps),
                            "description": step.description,
                            "tool": step.tool,
                            "action": step.action,
                            "status": step.status.value,
                            "progress": (i / len(plan.steps)) * 100,
                        },
                    }
                )

                # 发送思维过程
                await self._send_step_thinking(step, plan.id, session_id)

                # 执行步骤
                success = await self._execute_step_streaming(step, plan.id, session_id)

                if success:
                    step.status = TaskStatus.COMPLETED
                    self.console.print(f"✅ 步骤 {step.id} 执行成功")
                    self._log(f"✅ 步骤 {step.id} 执行成功")
                else:
                    step.status = TaskStatus.FAILED
                    self.console.print(f"❌ 步骤 {step.id} 执行失败")
                    self._log(f"❌ 步骤 {step.id} 执行失败", "ERROR")

                # 发送步骤完成的WebSocket消息
                await self._send_websocket_message(
                    {
                        "type": "step_complete",
                        "data": {
                            "plan_id": plan.id,
                            "session_id": session_id,
                            "step_id": step.id,
                            "step_number": i + 1,
                            "total_steps": len(plan.steps),
                            "status": step.status.value,
                            "result": step.result,
                            "progress": ((i + 1) / len(plan.steps)) * 100,
                        },
                    }
                )

                await asyncio.sleep(0.1)  # 短暂延迟以确保消息顺序

            plan.status = TaskStatus.COMPLETED
            self.console.print(f"🎉 流式计划执行完成!")
            self._log(f"🎉 流式计划执行完成 - 计划ID: {plan.id}")

            # 生成智能回复
            intelligent_response = await self._generate_intelligent_response(plan)

            # 发送执行完成的WebSocket消息
            await self._send_websocket_message(
                {
                    "type": "execution_complete",
                    "data": {
                        "plan_id": plan.id,
                        "session_id": session_id,
                        "status": plan.status.value,
                        "intelligent_response": intelligent_response,
                        "summary": f"任务执行完成，共完成 {len(plan.steps)} 个步骤",
                    },
                }
            )

            return True

        except Exception as e:
            plan.status = TaskStatus.FAILED
            self.console.print(f"💥 流式计划执行失败，错误: {str(e)}")
            self._log(
                f"❌ 流式计划执行失败 - 计划ID: {plan.id}, 错误: {str(e)}", "ERROR"
            )

            # 发送执行失败的WebSocket消息
            await self._send_websocket_message(
                {
                    "type": "execution_error",
                    "data": {
                        "plan_id": plan.id,
                        "session_id": session_id,
                        "error": str(e),
                    },
                }
            )

            return False

        finally:
            self.active_executions.pop(plan.id, None)

    async def _send_step_thinking(self, step: TaskStep, plan_id: str, session_id: str):
        """发送步骤思维过程"""
        thinking_steps = [
            f"🤔 分析任务需求：{step.description}",
            f"🔧 选择工具：{step.tool}",
            f"📝 准备参数：{step.parameters}",
            f"⚡ 开始执行 {step.action} 操作",
        ]

        for i, thinking in enumerate(thinking_steps):
            await self._send_websocket_message(
                {
                    "type": "cot_step",
                    "data": {
                        "plan_id": plan_id,
                        "session_id": session_id,
                        "step_id": step.id,
                        "content": thinking,
                        "timestamp": asyncio.get_event_loop().time(),
                        "stage": f"thinking_{i + 1}",
                    },
                }
            )
            await asyncio.sleep(0.3)  # 模拟思考时间

    async def _execute_step_streaming(
        self, step: TaskStep, plan_id: str, session_id: str
    ) -> bool:
        """执行单个步骤并发送流式更新"""

        step.status = TaskStatus.IN_PROGRESS
        self._log(f"⚡ 开始流式执行工具调用 - {step.tool}.{step.action}")

        try:
            # 发送工具执行开始通知
            await self._send_websocket_message(
                {
                    "type": "tool_execution_start",
                    "data": {
                        "plan_id": plan_id,
                        "session_id": session_id,
                        "step_id": step.id,
                        "tool": step.tool,
                        "action": step.action,
                        "parameters": step.parameters,
                        "message": f"正在执行 {step.tool}.{step.action}...",
                    },
                }
            )

            # 获取适当的工具
            tool = self.tool_manager.get_tool(step.tool)
            if not tool:
                step.error = f"工具 '{step.tool}' 未找到"
                self._log(f"❌ 工具未找到: {step.tool}", "ERROR")

                await self._send_websocket_message(
                    {
                        "type": "tool_execution_error",
                        "data": {
                            "plan_id": plan_id,
                            "session_id": session_id,
                            "step_id": step.id,
                            "error": step.error,
                        },
                    }
                )
                return False

            self._log(f"🔧 工具分发 - 工具: {step.tool}, 动作: {step.action}")

            # 发送工具执行进度
            await self._send_websocket_message(
                {
                    "type": "tool_execution_progress",
                    "data": {
                        "plan_id": plan_id,
                        "session_id": session_id,
                        "step_id": step.id,
                        "message": "工具执行中...",
                        "progress": 50,
                    },
                }
            )

            # 执行工具
            result = await tool.execute(step.action, step.parameters)
            step.result = str(result)

            # 使用任务日志记录器记录工具执行
            if self.task_logger:
                self.task_logger.log_tool_execution(
                    tool_name=step.tool,
                    action=step.action,
                    parameters=step.parameters,
                    result=result,
                )

            result_length = len(step.result) if step.result else 0
            self._log(f"💾 工具执行结果已保存 - 长度: {result_length} 字符")

            # 发送工具调用完成的WebSocket消息
            await self._send_websocket_message(
                {
                    "type": "tool_call",
                    "data": {
                        "plan_id": plan_id,
                        "session_id": session_id,
                        "step_id": step.id,
                        "tool": step.tool,
                        "action": step.action,
                        "parameters": step.parameters,
                        "result": step.result,
                        "timestamp": asyncio.get_event_loop().time(),
                        "execution_time": "实际执行",
                        "status": "completed",
                    },
                }
            )

            return True

        except Exception as e:
            step.error = str(e)

            # 使用任务日志记录器记录工具执行错误
            if self.task_logger:
                self.task_logger.log_tool_execution(
                    tool_name=step.tool,
                    action=step.action,
                    parameters=step.parameters,
                    error=str(e),
                )

            self._log(
                f"❌ 工具执行失败 - {step.tool}.{step.action}, 错误: {str(e)}", "ERROR"
            )

            # 发送工具执行错误通知
            await self._send_websocket_message(
                {
                    "type": "tool_execution_error",
                    "data": {
                        "plan_id": plan_id,
                        "session_id": session_id,
                        "step_id": step.id,
                        "error": str(e),
                    },
                }
            )

            return False
